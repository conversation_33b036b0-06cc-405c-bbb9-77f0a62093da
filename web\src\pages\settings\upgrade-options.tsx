import { useEffect, useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import * as settings from 'api/models/settings';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import { Alert } from '@/components/alert';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { Loading } from '@/components/loading';
import {
  getData,
  clearError,
  clearState,
  selectError,
  selectIsLoading,
  selectOptions,
  deleteUpgradeOption,
} from '@/components/settings/upgrade-options/upgrade-options-slice';
import { UpgradeOptionDetail } from '@/components/settings/upgrade-options/upgrade-option-detail';

export default function UpgradeOptions() {
  const dispatch = useAppDispatch(),
    isLoading = useAppSelector(selectIsLoading),
    error = useAppSelector(selectError),
    options = useAppSelector(selectOptions),
    [selectedOption, setSelectedOption] =
      useState<settings.UpgradeOption | null>(null),
    [showDeleteConfirm, setShowDeleteConfirm] =
      useState<settings.UpgradeOption | null>(null);

  useEffect(() => {
    dispatch(clearState());
    dispatch(getData());
  }, [dispatch]);

  const handleClearError = () => {
    dispatch(clearError());
  };

  const handleEditOptionClick = (option: settings.UpgradeOption) => {
    setSelectedOption(option);
  };

  const handleOptionDetailClose = () => {
    setSelectedOption(null);
  };

  const handleNewClick = () => {
    setSelectedOption({
      id: 0,
      containerPickDescription: null,
      origins: null,
      costs: null,
    });
  };

  const handleDeleteClick = (option: settings.UpgradeOption) => {
    setShowDeleteConfirm(option);
  };

  const handleDeleteConfirm = async () => {
    if (showDeleteConfirm) {
      const response = await dispatch(
        deleteUpgradeOption(showDeleteConfirm.id)
      );
      if (!('error' in response)) {
        setShowDeleteConfirm(null);
      }
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteConfirm(null);
  };

  return (
    <>
      <Head>
        <title>Settings: Upgrade Options</title>
      </Head>
      <header className="sticky top-0 z-20 flex-wrap bg-white shadow">
        <div className="mx-auto flex max-w-7xl items-center justify-between px-8 py-6">
          <h2 className="flex-shrink-0 text-2xl font-bold leading-7 text-gray-900">
            <Icon icon="hat-beach" />
            &nbsp; Upgrade Option Settings
          </h2>
          <div className="flex">
            <button
              type="button"
              className="btn-new flex flex-nowrap"
              onClick={handleNewClick}
            >
              <Icon icon="plus-circle" className="flex" />
              &nbsp;
              <div className="flex whitespace-nowrap">New Upgrade Option</div>
            </button>
            <Link
              href={routes.settings.home.to()}
              className="btn-secondary ml-2"
            >
              Close
            </Link>
          </div>
        </div>
      </header>
      <main className="mx-auto flex w-full max-w-7xl flex-grow overflow-auto">
        <div className="mx-auto h-full w-full px-8">
          <Error error={error} clear={handleClearError} />
          {isLoading && <Loading />}
          <div className="mt-8 flex h-full flex-col">
            <div className="-mx-8 -my-2 h-full">
              <div className="inline-block min-w-full px-8 py-2 align-middle">
                <div className="rounded-lg shadow ring-1 ring-black ring-opacity-5">
                  <table className="min-w-full divide-y divide-gray-300">
                    <thead>
                      <tr className="sticky top-0">
                        <th className="w-[1%] bg-gray-100 px-2 py-3.5 text-left text-gray-900">
                          &nbsp;
                        </th>
                        <th className="bg-gray-100 px-2 py-3.5 text-left text-gray-900">
                          Container / Pick Description
                        </th>
                        <th className="bg-gray-100 px-2 py-3.5 text-center text-gray-900">
                          Origins
                        </th>
                        <th className="bg-gray-100 px-2 py-3.5 text-center text-gray-900">
                          Costs
                        </th>
                        <th className="w-[1%] bg-gray-100 px-2 py-3.5 text-left text-gray-900">
                          &nbsp;
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white">
                      {options.map((option) => (
                        <tr key={option.id} className="border-b">
                          <td className="w-[1%] whitespace-nowrap p-2">
                            <button
                              type="button"
                              onClick={() => handleEditOptionClick(option)}
                              className="text-blue-600"
                            >
                              <Icon icon="edit" />
                            </button>
                          </td>
                          <td className="whitespace-nowrap px-2 py-4 text-left">
                            {option.containerPickDescription}
                          </td>
                          <td className="whitespace-nowrap px-2 py-4 text-center">
                            {option.origins}
                          </td>
                          <td className="whitespace-nowrap px-2 py-4 text-center">
                            {option.costs}
                          </td>
                          <td className="w-[1%] whitespace-nowrap p-2 text-center">
                            <button
                              type="button"
                              onClick={() => handleDeleteClick(option)}
                              className="btn-delete"
                            >
                              <Icon icon="trash" />
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
        {!!selectedOption && (
          <UpgradeOptionDetail
            option={selectedOption}
            close={handleOptionDetailClose}
          />
        )}
        <Alert
          open={!!showDeleteConfirm}
          colour="danger"
          title="Delete Upgrade Option"
          message="Are you sure you want to delete this upgrade option?"
          confirmButtonText="Yes"
          cancelButtonText="No"
          confirm={handleDeleteConfirm}
          cancel={handleDeleteCancel}
        />
      </main>
    </>
  );
}
