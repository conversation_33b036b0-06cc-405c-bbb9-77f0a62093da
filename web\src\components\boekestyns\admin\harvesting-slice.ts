import {
  createSlice,
  AsyncThunk,
  createAsyncThunk,
  createSelector,
} from '@reduxjs/toolkit';
import { boekestynHarvestingApi } from 'api/boekestyn-harvesting-service';
import * as boeks from 'api/models/boekestyns';
import { RootState } from '@/services/store';
import { ProblemDetails } from '@/utils/problem-details';
import { sortBy } from '@/utils/sort';

const sortByPlantName = sortBy('name');

export const ScheduleHarvestingOrderType = 'SCHEDULE_HARVESTING_ORDER';
export const SortHarvestingWorkOrderType = 'SORT_HARVESTING_WORK_ORDER';

export type SortFields = keyof boeks.HarvestingAdminOrderItem;

export const getScheduleById: AsyncThunk<
  boeks.HarvestingAdminOrderItem | undefined,
  string,
  { state: RootState }
> = createAsyncThunk(
  'boekestyn-harvesting/getScheduleById',
  async (id, { getState, rejectWithValue }) => {
    try {
      const { boekestynHarvestingAdmin: boekestynHarvesting } =
          getState() as RootState,
        orders = boekestynHarvesting.orders,
        order = orders.find((o) => o.orderId === id);

      return order;
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

interface MoveItemArgs {
  scheduleId: number;
  movingItem: boeks.HarvestingWorkOrder;
  existingItem: boeks.HarvestingWorkOrder;
}

export const moveItem: AsyncThunk<
  boeks.HarvestingWorkOrder[] | undefined,
  MoveItemArgs,
  { state: RootState }
> = createAsyncThunk(
  'boekestyn-harvesting/moveItem',
  async (
    { scheduleId, movingItem, existingItem },
    { getState, rejectWithValue }
  ) => {
    try {
      const { boekestynHarvestingAdmin: boekestynHarvesting } =
          getState() as RootState,
        schedule = boekestynHarvesting.schedules.find(
          (s) => s.id === scheduleId
        );

      if (schedule) {
        const copy = schedule.workOrders
            .filter((i) => i.id !== movingItem.id)
            .map((i) => ({ ...i })),
          index = copy.findIndex((i) => i.id === existingItem.id);

        if (movingItem) {
          copy.splice(index, 0, { ...movingItem });

          copy.forEach((i, index) => (i.sortOrder = index + 1));

          return copy;
        }
      }
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

interface HarvestingState {
  orders: boeks.HarvestingAdminOrderItem[];
  lines: boeks.HarvestingLine[];
  schedules: boeks.HarvestingSchedule[];
  sort: SortFields;
  sortDescending?: boolean;
  plant: string | null;
}

const initialState: HarvestingState = {
  orders: [],
  lines: [],
  schedules: [],
  sort: 'flowerDate',
  sortDescending: false,
  plant: null,
};

export interface SortArgs {
  sort: SortFields;
  sortDescending?: boolean;
}

const harvestingSlice = createSlice({
  name: 'harvesting',
  initialState,
  reducers: {
    setSort(state, { payload }: { payload: SortArgs }) {
      state.sort = payload.sort;
      state.sortDescending = payload.sortDescending;
    },
    setPlant(state, { payload }: { payload: string }) {
      state.plant = payload;
    },
  },
  extraReducers: (builder) =>
    builder
      .addMatcher(
        boekestynHarvestingApi.endpoints.harvesting.matchFulfilled,
        (state, { payload }) => {
          state.lines = payload.lines;
        }
      )
      .addMatcher(
        boekestynHarvestingApi.endpoints.harvestingOrders.matchFulfilled,
        (state, { payload }) => {
          state.orders = payload.orders;
        }
      )
      .addMatcher(
        boekestynHarvestingApi.endpoints.harvestingSchedules.matchFulfilled,
        (state, { payload }) => {
          state.schedules = payload.schedules;
        }
      ),
});

export const { setSort, setPlant } = harvestingSlice.actions;

const selectAllOrders = ({
  boekestynHarvestingAdmin: boekestynHarvesting,
}: RootState) => boekestynHarvesting.orders;
export const selectLines = ({
  boekestynHarvestingAdmin: boekestynHarvesting,
}: RootState) => boekestynHarvesting.lines;
export const selectSchedules = ({
  boekestynHarvestingAdmin: boekestynHarvesting,
}: RootState) => boekestynHarvesting.schedules;
export const selectSort = ({
  boekestynHarvestingAdmin: boekestynHarvesting,
}: RootState) => boekestynHarvesting.sort;
export const selectSortDescending = ({
  boekestynHarvestingAdmin: boekestynHarvesting,
}: RootState) => boekestynHarvesting.sortDescending;
export const selectPlant = ({
  boekestynHarvestingAdmin: boekestynHarvesting,
}: RootState) => boekestynHarvesting.plant;

// export const selectPlants = createSelector(selectAllOrders, (orders) => {
//   const plants = orders
//     .reduce((memo, o) => {
//       if (!memo.some((p) => p._id === o.plantCrop)) {
//         memo.push({
//           _id: o.plantCrop,
//           name: o.plantCrop,
//         });
//       }
//       return memo;
//     }, [] as boeks.HarvestingOrderPlant[])
//     .sort(sortByPlantName);
//   return [...new Set(plants)];
// });

export const selectOrders = createSelector(
  selectAllOrders,
  selectSort,
  selectSortDescending,
  (orders, sort, sortDescending) =>
    orders
      //.filter((o) => !plant || o.plant._id === plant)
      .map((o) => ({ ...o }))
      .sort(sortBy(sort, sortDescending ? 'descending' : ''))
);

export default harvestingSlice.reducer;
