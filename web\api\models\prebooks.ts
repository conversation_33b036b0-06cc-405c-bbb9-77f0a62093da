export const overAndAboveBlanketComment =
  'Quantity is over and above blanket quantity.';

export interface PrebookListItem {
  id: number;
  name: string | null;
  requiredDate: string | null;
  seasonDate: string | null;
  isBlanket: boolean;
  blanketStartDate: string | null;
  vendor: string | null;
  salesperson: string | null;
  customer: string | null;
  shipTo: string | null;
  season: string | null;
  boxCode: string | null;
  caseCount: number;
  spirePurchaseOrderNumber: string | null;
  futureOrderId: number | null;
  created: string;
  createdBy: string;
  modified: string;
  modifiedBy: string;
  sent: string | null;
  sentBy: string | null;
  confirmed: string | null;
  confirmedBy: string | null;
}

export interface PrebookSummaryItem {
  id: number;
  customer: string | null;
  vendor: string | null;
  shipTo: string | null;
  salesperson: string | null;
  season: string | null;
  isBlanket: boolean;
  date: string | null;
  week: number | null;
  weekYear: number | null;
  month: number | null;
  year: number | null;
  spirePartNumber: string;
  description: string;
  orderQuantity: number;
  prebookIdList: string;
}

export interface BlanketItemListItem {
  id: number;
  prebookId: number;
  customer: string | null;
  shipTo: string | null;
  vendor: string;
  salesperson: string | null;
  season: string | null;
  requiredDate: string;
  seasonDate: string | null;
  blanketStartDate: string | null;
  blanketWeekId: string | null;
  spirePartNumber: string;
  description: string;
  blanketQuantity: number;
  bookedQuantity: number;
  created: string;
  createdBy: string;
  modified: string;
  modifiedBy: string;
  sent: string | null;
  sentBy: string | null;
  confirmed: string | null;
  confirmedBy: string | null;

  bookingItems: BlanketItemListBookingItem[];
}

export interface BlanketItemListBookingItem {
  id: number;
  prebookId: number | null;
  futureOrderId: number;
  blanketItemId: number;
  customer: string | null;
  shipTo: string | null;
  salesperson: string | null;
  season: string | null;
  requiredDate: string | null;
  orderQuantity: number;
}

export interface PrebookDetail {
  id: number;
  name: string | null;
  requiredDate: string | null;
  isBlanket: boolean;
  blanketStartDate: string | null;
  blanketIsClosed: boolean;
  seasonName: string | null;
  boxCode: string | null;
  vendorId: number | null;
  vendorName: string | null;
  salespersonId: number | null;
  salespersonName: string | null;
  customerId: number | null;
  customerName: string | null;
  shipToId: number | null;
  shipToName: string | null;
  comments: string | null;
  growerItemNotes: string | null;
  spirePurchaseOrderId: number | null;
  spirePurchaseOrderNumber: string | null;
  futureOrderId: number | null;
  created: string;
  createdBy: string;
  modified: string;
  modifiedBy: string;
  confirmed: string | null;
  confirmedBy: string | null;
  deleted: string | null;
  deletedBy: string | null;

  items: PrebookDetailItem[];
}

export interface NewPrebookDetailItem {
  id: number;
  spireInventoryId?: number | null;
  spirePartNumber?: string | null;
  description?: string | null;
  orderQuantity: number;
  hasPotCover?: boolean;
  potCover?: string | null;
  dateCode?: string | null;
  upc?: string | null;
  weightsAndMeasures?: boolean;
  retail?: string | null;
  comments?: string | null;
  isApproximate: boolean;
  blanketItemId?: number | null;
  blanketWeekId?: string | null;
  upgradeSheet?: boolean;
  boekestynPlantId: string | null;
  boekestynCustomerAbbreviation: string | null;
  spirePurchaseOrderItemId?: number | null;
  upgradeLabourHours?: number;
  quantityPerFinishedItem?: number | null;
  specialPrice: number | null;
  growerItemNotes: string | null;
  boekPriority?: boolean;
  upcPrinted?: string | null;
  upcPrintedPrev?: string | null;

  blanketOptions?: PrebookDetailItemBlanketOption[];
  boekestynProducts?: PrebookItemBoekestynProduct[];
}

export interface PrebookDetailItem {
  id: number;
  spireInventoryId: number;
  spirePartNumber: string;
  description: string;
  orderQuantity: number;
  hasPotCover: boolean;
  potCover: string | null;
  dateCode: string | null;
  upc: string | null;
  weightsAndMeasures: boolean;
  retail: string | null;
  comments: string | null;
  isApproximate: boolean;
  blanketItemId: number | null;
  blanketWeekId: string | null;
  upgradeSheet: boolean;
  futureOrderItemId: number | null;
  boekestynPlantId: string | null;
  boekestynCustomerAbbreviation: string | null;
  spirePurchaseOrderItemId: number | null;
  upgradeLabourHours: number;
  quantityPerFinishedItem: number | null;
  specialPrice: number | null;
  growerItemNotes: string | null;
  boekPriority: boolean;
  upcPrinted: string | null;
  upcPrintedPrev: string | null;
  futureOrderId?: number | null;

  blanketOptions: PrebookDetailItemBlanketOption[];
  boekestynProducts: PrebookItemBoekestynProduct[];
}

export interface PrebookItemBoekestynProduct {
  prebookItemId: number;
  boekestynPlantId: string;
  boekestynCustomerAbbreviation: string | null;
  quantityPerFinishedItem: number;
}

export interface PrebookDetailItemBlanketOption {
  id: number;
  prebookItemId: number;
  spireInventoryId: number;
  spirePartNumber: string;
  description: string;
}

export interface PrebookCreate {
  name: string | null;
  requiredDate: string | null;
  isBlanket: boolean;
  blanketStartDate: string | null;
  blanketIsClosed: boolean;
  seasonName: string | null;
  boxCode: string | null;
  vendorId: number | null;
  vendorName: string | null;
  salespersonId: number | null;
  salespersonName: string | null;
  customerId: number | null;
  customerName: string | null;
  shipToId: number | null;
  shipToName: string | null;
  comments: string | null;
  growerItemNotes: string | null;

  items: PrebookDetailItem[];
}

export interface Season {
  name: string;
  seasonDate: string;
}

export interface ProductShipToDefault {
  id: number;
  spireInventoryId: number;
  shipToId: number;
  dateCode: string | null;
  hasPotCover: boolean;
  potCover: string | null;
  upc: string | null;
  weightsAndMeasures: boolean;
  retail: string | null;
  unitPrice: number | null;
  customerItemCode: string | null;
}

export interface PendingPrebookEmail {
  prebookId: number;
  vendorName: string;
  prebookDate: string;
  previousPrebookDate: string | null;
  requiredDate: string | null;
  seasonName: string | null;
  comments: string | null;

  items: PendingPrebookEmailItem[];
}

export interface PendingPrebookEmailItem {
  prebookItemId: number;
  spirePartNumber: string;
  description: string;
  orderQuantity: number;
  previousOrderQuantity: number | null;
  packQuantity: number | null;
  boxCode: string | null;
  dateCode: string | null;
  potCover: string | null;
  upc: string | null;
  weightsAndMeasures: boolean;
  retail: string | null;
  comments: string | null;
}

export interface PrebookEmail extends PendingPrebookEmail {
  id: number;
  created: string;
  createdBy: string;
  to: string;
  cc: string | null;
  bcc: string | null;
  from: string;
  subject: string;
  body: string;

  items: PrebookEmailItem[];
}

export interface PrebookEmailItem extends PendingPrebookEmailItem {
  id: number;
  prebookEmailId: number;
}

export interface PrebookBlanketItem {
  id: number;
  spireInventoryId: number;
  spirePartNumber: string;
  description: string;
  vendorId: number;
  vendorName: string;
  customerId: number | null;
  customerName: string | null;
  shipToId: number | null;
  shipToName: string | null;
  blanketStartDate: string | null;
  requiredDate: string;
  blanketWeekId: string | null;
  blanketQuantity: number;
  bookedQuantity: number;
  boekestynPlantId?: string | null;
  boekestynCustomerAbbreviation?: string | null;
}
