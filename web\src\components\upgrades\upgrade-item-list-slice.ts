import {
  createSelector,
  createSlice,
  PayloadAction,
  AsyncThunk,
  createAction,
  createAsyncThunk,
} from '@reduxjs/toolkit';
import { DateTime } from 'luxon';
import { boekestynApi } from 'api/boekestyn-service';
import {
  FutureOrderUpgradeItemsListDownloadArgs,
  futureOrdersListApi,
  futureOrdersApi,
} from 'api/future-orders-service';
import { prebooksApi } from 'api/prebooks-service';
import * as models from 'api/models/future-orders';
import * as settings from 'api/models/settings';
import { RootState } from '@/services/store';
import { ProblemDetails } from '@/utils/problem-details';
import { sort, sortBy } from '@/utils/sort';
import {
  settingsService,
  UpdateUpgradeOptionResponse,
} from 'api/settings-service';
import { formatNumber } from '@/utils/format';
import { contains, equals } from '@/utils/equals';
import { matchesBoxCode, matchesGroup } from './upgrade-functions';

export interface UpgradeItemDate {
  date: string;
  season: string | null;
  itemGroups: models.UpgradeItem[][];
  priority: boolean;
  isLast: boolean;
}

const sortByDate = sortBy('date'),
  sortBySeason = sortBy('season', 'descending'),
  sortByPriority = sortBy('priority', 'descending'),
  sortByLabourHours =
    (descending: boolean) => (a: models.UpgradeItem, b: models.UpgradeItem) => {
      const rawHours1 =
          a.labourHours === 0 ? 0 : a.orderQuantity / a.labourHours,
        labourHours1 = Math.round(
          rawHours1 > 0 && rawHours1 < 1 ? 1 : rawHours1
        ),
        rawHours2 = b.labourHours === 0 ? 0 : b.orderQuantity / b.labourHours,
        labourHours2 = Math.round(
          rawHours2 > 0 && rawHours2 < 1 ? 1 : rawHours2
        );

      return sort(labourHours1, labourHours2, descending ? 'descending' : '');
    },
  sortByBoxCode = sortBy('boxCode');

export const blankShipToFilter = 'No Ship To';
export const blankCustomerFilter = 'No Customer';

export const setItemPropertyValue: AsyncThunk<
  SetItemArgs,
  SetItemArgs,
  { state: RootState }
> = createAsyncThunk(
  'upgrade-item-list-setItemPropertyValue',
  async ({ id, propName, value }, { rejectWithValue }) => {
    try {
      const fieldName = upgradeFieldNameFromPropertyName(propName),
        args = { id, fieldName, value };

      await futureOrdersApi.updateUpgradeItemProperty(args);

      return { id, propName, value };
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

interface UpdateUpgradeOptionArgs {
  containerPickDescription: string;
  origins: string | null;
  costs: string | null;
}

export const updateUpgradeOption: AsyncThunk<
  UpdateUpgradeOptionResponse,
  UpdateUpgradeOptionArgs,
  { state: RootState }
> = createAsyncThunk(
  'upgrade-item-list-updateUpgradeOption',
  async ({ containerPickDescription, origins, costs }, { rejectWithValue }) => {
    try {
      return await settingsService.createOrUpdateUpgradeOption(
        containerPickDescription,
        origins,
        costs
      );
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const downloadUpgradeItemsList: AsyncThunk<
  void,
  FutureOrderUpgradeItemsListDownloadArgs,
  { state: RootState }
> = createAsyncThunk(
  'upgrade-item-list-downloadItemsList',
  async (args, { rejectWithValue }) => {
    try {
      return await futureOrdersApi.futureOrderUpgradeItemsListDownload(args);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const downloadUpgradeItemReport: AsyncThunk<
  void,
  FutureOrderUpgradeItemsListDownloadArgs,
  { state: RootState }
> = createAsyncThunk(
  'upgrade-item-list-downloadUpgradeItemReport',
  async (args, { rejectWithValue }) => {
    try {
      return await futureOrdersApi.futureOrderUpgradeItemsListReport(args);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const setUpgradeConfirmed: AsyncThunk<
  unknown,
  number,
  { state: RootState }
> = createAsyncThunk(
  'upgrade-item-list-setUpgradeConfirmed',
  async (id, { rejectWithValue }) => {
    try {
      return await prebooksApi.confirmUpgradeItem(id);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

interface SetPriorityArgs {
  id: number;
  priority: boolean;
}

export const setPriority: AsyncThunk<
  undefined,
  SetPriorityArgs,
  { state: RootState }
> = createAsyncThunk(
  'boekestyn-list-setPriority',
  async ({ id, priority }, { rejectWithValue, dispatch }) => {
    try {
      await boekestynApi.setPrebookItemPriority(id, priority);
      dispatch(
        futureOrderUpgradeItemListSlice.actions.setPriority({ id, priority })
      );
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const setUpcPrinted: AsyncThunk<
  undefined,
  number,
  { state: RootState }
> = createAsyncThunk(
  'boekestyn-list-setUpcPrinted',
  async (id, { rejectWithValue, dispatch }) => {
    try {
      await boekestynApi.completePrebookItemUpc(id);
      dispatch(futureOrderUpgradeItemListSlice.actions.setUpcPrinted(id));
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const setUpcUnprinted: AsyncThunk<
  undefined,
  number,
  { state: RootState }
> = createAsyncThunk(
  'boekestyn-list-setUpcUnprinted',
  async (id, { rejectWithValue, dispatch }) => {
    try {
      await boekestynApi.uncompletePrebookItemUpc(id);
      dispatch(futureOrderUpgradeItemListSlice.actions.setUpcUnprinted(id));
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

const downloadUpgradeItemsListPending = createAction(
    downloadUpgradeItemsList.pending.type
  ),
  downloadUpgradeItemsListFulfilled = createAction(
    downloadUpgradeItemsList.fulfilled.type
  ),
  downloadUpgradeItemsListRejected = createAction<ProblemDetails>(
    downloadUpgradeItemsList.rejected.type
  ),
  setUpgradeConfirmedRejected = createAction<ProblemDetails>(
    setUpgradeConfirmed.rejected.type
  ),
  setItemPropertyValueFulfilled = createAction<SetItemArgs>(
    setItemPropertyValue.fulfilled.type
  ),
  setItemPropertyValueRejected = createAction<ProblemDetails>(
    setItemPropertyValue.rejected.type
  ),
  updateUpgradeOptionFulfilled = createAction<UpdateUpgradeOptionResponse>(
    updateUpgradeOption.fulfilled.type
  ),
  updateUpgradeOptionRejected = createAction<ProblemDetails>(
    updateUpgradeOption.rejected.type
  );

interface UpgradeItemFilters {
  customer: string | null;
  shipTo: string | null;
  showConfirmed: boolean;
  showUnconfirmed: boolean;
}

interface FutureOrderUpgradeItemState {
  startDate: string;
  endDate: string;
  search: string;
  itemsSort: keyof models.UpgradeItem;
  sortItemsDescending: boolean;
  filter: UpgradeItemFilters;
  items: models.UpgradeItem[];
  dates: UpgradeItemDate[];
  upgradeOptions: settings.UpgradeOption[];
  attachments: models.UpgradeItemAttachment[];
  printWithAdditionsDate: string | null;
  isLoading: boolean;
  error: ProblemDetails | null;
}

const initialState: FutureOrderUpgradeItemState = {
  startDate: DateTime.now().toFormat('yyyy-MM-dd'),
  endDate: '',
  search: '',
  itemsSort: 'date',
  sortItemsDescending: false,
  filter: {
    customer: null,
    shipTo: null,
    showConfirmed: true,
    showUnconfirmed: true,
  },
  items: [],
  dates: [],
  attachments: [],
  upgradeOptions: [],
  printWithAdditionsDate: null,
  isLoading: false,
  error: null,
};

export interface UpgradeItemSortArgs {
  sort: keyof models.UpgradeItem;
  sortDescending: boolean;
}

export interface SetItemArgs {
  id: number;
  propName: keyof models.UpgradeItem;
  value: string | number | boolean | null;
}

export const futureOrderUpgradeItemListSlice = createSlice({
  name: 'future-order-upgrade-item-list',
  initialState,
  reducers: {
    clearError(state) {
      state.error = null;
    },
    clearSearchAndFilters(state) {
      return {
        ...state,
        startDate: DateTime.now().toFormat('yyyy-MM-dd'),
        endDate: '',
        search: '',
        itemsSort: 'date',
        sortItemsDescending: false,
        filter: {
          customer: null,
          shipTo: null,
          showConfirmed: true,
          showUnconfirmed: true,
        },
      };
    },
    setStartDate(state, { payload }: PayloadAction<string>) {
      state.startDate = payload;
    },
    setEndDate(state, { payload }: PayloadAction<string>) {
      state.endDate = payload;
    },
    setSearch(state, { payload }: PayloadAction<string>) {
      state.search = payload;
    },
    setItemSort(state, { payload }: PayloadAction<UpgradeItemSortArgs>) {
      state.itemsSort = payload.sort;
      state.sortItemsDescending = payload.sortDescending;
    },
    setCustomerFilter(state, { payload }: PayloadAction<string | null>) {
      const filter = { ...state.filter, customer: payload };
      state.filter = filter;
    },
    setShipToFilter(state, { payload }: PayloadAction<string | null>) {
      const filter = { ...state.filter, shipTo: payload };
      state.filter = filter;
    },
    setShowConfirmed(
      state,
      {
        payload,
      }: PayloadAction<{ showConfirmed: boolean; showUnconfirmed: boolean }>
    ) {
      const filter = { ...state.filter, ...payload };
      state.filter = filter;
    },
    setPrintWithAdditionsDate(
      state,
      { payload }: PayloadAction<string | null>
    ) {
      state.printWithAdditionsDate = payload;
    },
    setPriority(state, { payload }: PayloadAction<SetPriorityArgs>) {
      const items = state.items.map((t) => ({ ...t })),
        item = items.find((i) => i.id === payload.id);
      if (item) {
        item.priority = payload.priority;
      }

      state.items = items;
    },
    setUpcPrinted(state, { payload }: PayloadAction<number>) {
      const items = state.items.map((t) => ({ ...t })),
        item = items.find((i) => i.id === payload);
      if (item) {
        item.upcPrinted = new Date().toISOString();
      }

      state.items = items;
    },
    setUpcUnprinted(state, { payload }: PayloadAction<number>) {
      const items = state.items.map((t) => ({ ...t })),
        item = items.find((i) => i.id === payload);
      if (item) {
        item.upcPrinted = null;
      }

      state.items = items;
    },
    addAttachments(
      state,
      { payload }: PayloadAction<models.UpgradeItemAttachment[]>
    ) {
      state.attachments = state.attachments.concat(payload);
    },
    deleteAttachments(
      state,
      { payload }: PayloadAction<models.UpgradeItemAttachment[]>
    ) {
      const ids = payload.map((a) => a.id),
        attachments = state.attachments.filter((a) => !ids.includes(a.id));

      state.attachments = attachments;
    },
  },
  extraReducers: (builder) =>
    builder
      .addCase(downloadUpgradeItemsListPending, (state) => {
        state.isLoading = true;
      })
      .addCase(downloadUpgradeItemsListFulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(downloadUpgradeItemsListRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addCase(setUpgradeConfirmedRejected, (state, { payload }) => {
        state.error = payload;
      })
      .addCase(setItemPropertyValueFulfilled, (state, { payload }) => {
        const items = state.items.map((i) => ({ ...i })),
          item = items.find((i) => i.id === payload.id);

        if (item) {
          // @ts-ignore
          item[payload.propName] = payload.value;
        }

        state.items = items;
      })
      .addCase(setItemPropertyValueRejected, (state, { payload }) => {
        state.error = payload;
      })
      .addCase(updateUpgradeOptionFulfilled, (state, { payload }) => {
        state.upgradeOptions = payload.upgradeOptions;
      })
      .addCase(updateUpgradeOptionRejected, (state, { payload }) => {
        state.error = payload;
      })
      .addMatcher(
        futureOrdersListApi.endpoints.upgradeItems.matchPending,
        (state) => {
          state.isLoading = true;
        }
      )
      .addMatcher(
        futureOrdersListApi.endpoints.upgradeItems.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.items = payload.items;
          state.attachments = payload.attachments;
          state.upgradeOptions = payload.upgradeOptions;
        }
      )
      .addMatcher(
        futureOrdersListApi.endpoints.upgradeItems.matchRejected,
        (state, { payload }) => {
          state.isLoading = false;
          if (payload) {
            state.error = payload;
          }
        }
      ),
});

export const {
  clearError,
  clearSearchAndFilters,
  setStartDate,
  setEndDate,
  setSearch,
  setItemSort,
  setPrintWithAdditionsDate,
  setCustomerFilter,
  setShipToFilter,
  setShowConfirmed,
  addAttachments,
  deleteAttachments,
} = futureOrderUpgradeItemListSlice.actions;

export const selectError = (state: RootState) =>
  state.futureOrderUpgradeItemsList.error;
export const selectStartDate = (state: RootState) =>
  state.futureOrderUpgradeItemsList.startDate;
export const selectEndDate = (state: RootState) =>
  state.futureOrderUpgradeItemsList.endDate;
export const selectSearch = (state: RootState) =>
  state.futureOrderUpgradeItemsList.search;
export const selectItemsSort = (state: RootState) =>
  state.futureOrderUpgradeItemsList.itemsSort;
export const selectSortItemsDescending = (state: RootState) =>
  state.futureOrderUpgradeItemsList.sortItemsDescending;
export const selectFilter = (state: RootState) =>
  state.futureOrderUpgradeItemsList.filter;
export const selectPrintWithAdditionsDate = (state: RootState) =>
  state.futureOrderUpgradeItemsList.printWithAdditionsDate;
export const selectIsLoading = (state: RootState) =>
  state.futureOrderUpgradeItemsList.isLoading;
export const selectUpgradeOptions = (state: RootState) =>
  state.futureOrderUpgradeItemsList.upgradeOptions;
export const selectAttachments = (state: RootState) =>
  state.futureOrderUpgradeItemsList.attachments;

const selectAllUpgradeItems = (state: RootState) =>
  state.futureOrderUpgradeItemsList.items;

export const selectItemCustomers = createSelector(
  selectAllUpgradeItems,
  (items) => {
    const customers = items
      .reduce(
        (memo, p) =>
          p.customer && memo.indexOf(p.customer) === -1
            ? memo.concat([p.customer])
            : memo,
        [] as string[]
      )
      .sort();

    if (items.some((o) => !o.customer)) {
      customers.unshift(blankCustomerFilter);
    }

    return customers;
  }
);
export const selectItemShipTos = createSelector(
  selectAllUpgradeItems,
  selectFilter,
  (items, { customer }) => {
    const shipTos = items
      // if there's a customer filter, only show those ones
      .filter(
        (o) =>
          !customer ||
          equals(customer, o.customer) ||
          (customer === blankCustomerFilter && !o.customer)
      )
      .reduce(
        (memo, p) =>
          p.shipTo && memo.indexOf(p.shipTo) === -1
            ? memo.concat([p.shipTo])
            : memo,
        [] as string[]
      )
      .sort();

    if (items.some((o) => !o.shipTo)) {
      shipTos.unshift(blankShipToFilter);
    }

    return shipTos;
  }
);

export const selectUpgradeItems = createSelector(
  selectAllUpgradeItems,
  selectSearch,
  selectItemsSort,
  selectSortItemsDescending,
  selectFilter,
  (items, search, sortField, sortDescending, filter) => {
    const sortByField =
        sortField === 'labourHours'
          ? sortByLabourHours(sortDescending)
          : sortBy(sortField, sortDescending ? 'descending' : ''),
      sort = (a: models.UpgradeItem, b: models.UpgradeItem) =>
        sortByDate(a, b) || sortByPriority(a, b) || sortByField(a, b),
      list = items
        .map((o) => ({ ...o } as models.UpgradeItem))
        .filter(
          (o) =>
            (!search ||
              contains(formatNumber(o.prebookId, '00000'), search) ||
              contains(o.productComingFrom, search) ||
              contains(o.spirePartNumber, search) ||
              contains(o.description, search) ||
              contains(o.comments, search) ||
              contains(o.containerPickDescription, search) ||
              contains(o.upgradeComments, search) ||
              contains(o.boxCode, search) ||
              contains(o.customer, search) ||
              contains(o.shipTo, search)) &&
            (!filter.customer ||
              equals(filter.customer, o.customer) ||
              (filter.customer === blankCustomerFilter && !o.customer)) &&
            (!filter.shipTo ||
              equals(filter.shipTo, o.shipTo) ||
              (filter.shipTo === blankShipToFilter && !o.shipTo)) &&
            (filter.showConfirmed || !o.upgradeConfirmed) &&
            (filter.showUnconfirmed || o.upgradeConfirmed)
        )
        .sort(sort);

    return list;
  }
);

export const selectItemDates = createSelector(selectUpgradeItems, (items) =>
  items
    .reduce((memo, item) => {
      if (item.date) {
        const existingGroup = memo.find(
          (m) => m.date === item.date && m.season === item.season
        );
        if (!existingGroup) {
          memo.push({
            date: item.date,
            season: item.season,
            itemGroups: [[item]],
            priority: item.priority,
            isLast: false,
          });
        } else {
          const existingItemList = existingGroup.itemGroups.find((g) =>
            matchesGroup(item, g)
          );
          if (existingItemList) {
            existingItemList.push(item);
            existingItemList.sort(sortByBoxCode);
          } else {
            existingGroup.itemGroups.push([item]);
          }
        }
      }
      return memo;
    }, [] as UpgradeItemDate[])
    .sort(
      (a, b) => sortByDate(a, b) || sortByPriority(a, b) || sortBySeason(a, b)
    )
);

function upgradeFieldNameFromPropertyName(propName: keyof models.UpgradeItem) {
  switch (propName) {
    case 'containerPickDescription':
      return 'upgrade_container_pick_description';
    case 'labourHours':
      return 'upgrade_labour_hours';
    case 'productComingFrom':
      return 'upgrade_product_coming_from';
    case 'upcApprovalRequired':
      return 'upgrade_upc_approval_required';
    case 'upcComment':
      return 'upgrade_upc_comment';
    case 'upgradeComments':
      return 'upgrade_comments';
    case 'origins':
      return 'upgrade_origins';
    case 'costs':
      return 'upgrade_costs';
  }

  return propName;
}

export default futureOrderUpgradeItemListSlice.reducer;
