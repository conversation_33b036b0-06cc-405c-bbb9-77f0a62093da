import { useState, useCallback, useEffect, useMemo } from 'react';
import { DateTime, Duration } from 'luxon';
import {
  useStartStickingWorkOrderLabourMutation,
  usePauseStickingWorkOrderLabourMutation,
  useStopStickingWorkOrderLabourMutation,
} from 'api/boekestyn-sticking-service';
import * as boeks from 'api/models/boekestyns';
import { Icon } from '@/components/icon';
import { useAppSelector } from '@/services/hooks';
import { selectWorkOrders, selectDate } from './sticking-slice';
import { PauseLabourDialog } from './pause-labour-dialog';
import { formatNumber } from '@/utils/format';
import { StopLabourDialog } from './stop-labour-dialog';

interface TimingProps {
  workOrder: boeks.StickingWorkOrderItem;
}

export function Timing({ workOrder }: TimingProps) {
  const labour = workOrder.labour,
    workOrders = useAppSelector(selectWorkOrders),
    date = useAppSelector(selectDate),
    inProcess = useMemo(() => labour.find((l) => !l.endTime) || null, [labour]),
    isFinalLabour = useMemo(() => labour.some((l) => l.finalLabour), [labour]),
    [startLabour] = useStartStickingWorkOrderLabourMutation(),
    [pauseLabour] = usePauseStickingWorkOrderLabourMutation(),
    [stopLabour] = useStopStickingWorkOrderLabourMutation(),
    [ellapsed, setEllapsed] = useState(''),
    [timer, setTimer] = useState(0),
    [showPauseDialog, setShowPauseDialog] = useState(false),
    [showStopDialog, setShowStopDialog] = useState(false),
    isFirstAvailableLabour = useMemo(() => {
      const index = workOrders.findIndex((o) => o.id === workOrder.id),
        firstEligibleLabour = workOrders.find((o, i) => {
          return (
            i < index &&
            (!o.labour.length || o.labour.every((l) => !l.finalLabour))
          );
        });
      return !firstEligibleLabour;
    }, [workOrder.id, workOrders]),
    previousDaysLabour = useMemo(() => {
      if (labour.length) {
        const ellapsedMillis = labour.reduce((memo, l) => {
            if (!l.startTime) {
              return memo;
            }

            const now = DateTime.fromFormat(date, 'yyyy-MM-dd'),
              start = DateTime.fromFormat(l.startTime, 'yyyy-MM-dd HH:mm:ss'),
              isPrevious = start.diff(now, 'milliseconds').milliseconds < 0,
              started = start.toMillis(),
              ended = l.endTime
                ? DateTime.fromFormat(
                    l.endTime,
                    'yyyy-MM-dd HH:mm:ss'
                  ).toMillis()
                : Date.now().valueOf(),
              diff = ended - started;

            if (isPrevious) {
              return memo + diff;
            } else {
              return memo;
            }
          }, 0),
          duration = Duration.fromMillis(ellapsedMillis),
          ellapsed = ellapsedMillis
            ? ellapsedMillis < 60000
              ? `${duration.toFormat('s')} seconds`
              : `${duration.toFormat('m')} minutes`
            : '';

        return ellapsed;
      } else {
        return '';
      }
    }, [date, labour]);

  const update = useCallback(() => {
    if (labour.length) {
      const ellapsedMillis = labour.reduce((memo, l) => {
          if (!l.startTime) {
            return memo;
          }

          const now = DateTime.fromFormat(date, 'yyyy-MM-dd'),
            start = DateTime.fromFormat(l.startTime, 'yyyy-MM-dd HH:mm:ss'),
            isToday =
              start.hasSame(now, 'day') &&
              start.hasSame(now, 'month') &&
              start.hasSame(now, 'year'),
            started = start.toMillis(),
            ended = l.endTime
              ? DateTime.fromFormat(l.endTime, 'yyyy-MM-dd HH:mm:ss').toMillis()
              : Date.now().valueOf(),
            diff = ended - started;

          if (isToday) {
            return memo + diff;
          } else {
            return memo;
          }
        }, 0),
        todaysMillis = ellapsedMillis,
        duration = Duration.fromMillis(todaysMillis),
        ellapsed = todaysMillis
          ? todaysMillis < 60000
            ? `${duration.toFormat('s')} seconds`
            : `${duration.toFormat('m')} minutes`
          : '';

      setEllapsed(ellapsed);
    } else {
      setEllapsed('');
    }
  }, [date, labour]);

  useEffect(() => {
    if (timer) {
      window.clearInterval(timer);
    }

    const newTimer = window.setInterval(update, 1000);
    setTimer(newTimer);

    return () => {
      window.clearInterval(newTimer);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [update]);

  const handleStartClick = () => {
    startLabour({
      workOrderId: workOrder.id,
      crewSize: workOrder.crewSize,
    });
  };

  const handleStopClick = async () => {
    if (inProcess) {
      setShowStopDialog(true);
    }
  };

  const handleStopDialog = async (args: {
    varieties: { id: number; quantity: number }[];
  }) => {

    console.log(args.varieties);
    if (inProcess) {
      if (timer) {
        window.clearInterval(timer);
      }
      stopLabour({
        workOrderId: workOrder.id,
        crewSize: inProcess.crewSize,
        comments: inProcess.comments,
        varietyQuantities: args.varieties,
      });
    }

    setShowStopDialog(false);
  };

  const openPauseDialog = async () => {
    if (inProcess) {
      if (timer) {
        window.clearInterval(timer);
      }
      setShowPauseDialog(true);
    }
  };

  const handlePauseClick = async ({
    comments,
  }: {
    comments: string | null;
  }) => {
    if (inProcess) {
      pauseLabour({
        workOrderId: workOrder.id,
        crewSize: inProcess.crewSize,
        comments,
      });
    }

    setShowPauseDialog(false);
  };

  return (
    <>
      {isFirstAvailableLabour && !inProcess && !isFinalLabour && (
        <button
          type="button"
          className="btn-secondary"
          onClick={handleStartClick}
        >
          <Icon icon="play" />
        </button>
      )}
      {isFirstAvailableLabour && !!inProcess && (
        <>
          <button
            type="button"
            className="btn-secondary"
            onClick={openPauseDialog}
          >
            <Icon icon="pause" />
          </button>
          <button
            type="button"
            className="btn-secondary ml-2"
            onClick={handleStopClick}
          >
            <Icon icon="stop" />
          </button>
        </>
      )}
      {!!previousDaysLabour && (
        <div className="text-sm font-semibold italic text-zinc-500">
          Yesterday: {previousDaysLabour}
        </div>
      )}
      {!!ellapsed && <p>{ellapsed}</p>}
      {!!inProcess && <div className="italic text-zinc-500">Running</div>}
      {!inProcess && !!ellapsed && !isFinalLabour && (
        <div className="italic text-zinc-500">Paused</div>
      )}
      {isFinalLabour && <div className="font-light italic">Finished</div>}

      <PauseLabourDialog
        open={showPauseDialog}
        onClose={() => setShowPauseDialog(false)}
        onPause={handlePauseClick}
      />
      <StopLabourDialog
        open={showStopDialog}
        onClose={() => setShowStopDialog(false)}
        onStop={handleStopDialog}
        order={workOrder}
      />
    </>
  );
}
