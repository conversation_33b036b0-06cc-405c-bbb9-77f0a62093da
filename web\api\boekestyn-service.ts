import { AxiosError, AxiosResponseHeaders, ResponseType } from 'axios';
import { serializeError } from 'serialize-error';
import { createApi } from '@reduxjs/toolkit/query/react';
import axios, {
  ApiBase,
  axiosBaseQuery,
  downloadFile,
  getFilename,
} from './api-base';
import * as models from './models/boekestyns';
import * as couch from './models/couch';
import {
  createProblemDetails,
  isProblemDetails,
} from '@/utils/problem-details';
import { DateTime } from 'luxon';

const type =
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  responseType: ResponseType = 'blob';

class BoekestynService extends ApiBase {
  async boekestynItemListDownload(data: BoekestynItemListDownloadArgs) {
    try {
      const { headers, data: result } = await axios.post(
          '/boekestyns/items/download',
          data,
          { responseType }
        ),
        blob = new Blob([result], { type }),
        filename =
          getFilename(headers as AxiosResponseHeaders) || 'BoekestynItems.xlsx';

      downloadFile(blob, filename);
    } catch (ex) {
      if (ex instanceof Error) {
        const error = serializeError(ex);
        throw createProblemDetails(error.message || 'Error');
      }
      const e = ex as AxiosError;
      if (e.response) {
        if (isProblemDetails(e.response.data)) {
          throw e.response.data;
        } else if (typeof e.response.data === 'string') {
          throw createProblemDetails(e.response.data);
        }
      }

      if (e.response && e.response.data) {
        throw e.response.data;
      }

      throw e.message;
    }
  }

  async boekestynSalesDownload(data: BoekestynSalesDownloadArgs) {
    try {
      const { headers, data: result } = await axios.post(
          '/boekestyns/sales/download',
          data,
          { responseType }
        ),
        blob = new Blob([result], { type }),
        filename =
          getFilename(headers as AxiosResponseHeaders) || 'BoekestynSales.xlsx';

      downloadFile(blob, filename);
    } catch (ex) {
      if (ex instanceof Error) {
        const error = serializeError(ex);
        throw createProblemDetails(error.message || 'Error');
      }
      const e = ex as AxiosError;
      if (e.response) {
        if (isProblemDetails(e.response.data)) {
          throw e.response.data;
        } else if (typeof e.response.data === 'string') {
          throw createProblemDetails(e.response.data);
        }
      }

      if (e.response && e.response.data) {
        throw e.response.data;
      }

      throw e.message;
    }
  }

  async plants() {
    try {
      const { plants } = await this.get<BoekestynPlantResponse>(
        '/boekestyns/plants'
      );
      return plants;
    } catch (e) {
      throw e;
    }
  }

  async customers() {
    try {
      const { customers } = await this.get<BoekestynCustomerResponse>(
        '/boekestyns/customers'
      );
      return customers;
    } catch (e) {
      throw e;
    }
  }

  async productionOrders(): Promise<ProductionOrder[]> {
    try {
      const { orders } = await this.get<BoekestynProductionOrderResponse>(
          '/boekestyns/productionOrders'
        ),
        productionOrders = orders.map((o) => ({
          customer: o.customer.abbreviation,
          weekNumber: DateTime.fromISO(o.flowerDate).weekNumber,
          year: DateTime.fromISO(o.flowerDate).weekYear,
          plantId: o.plant._id,
          description: o.plant.name,
          cases: o.cases,
          salesWeeks: o.salesWeeks,
        }));

      return productionOrders;
    } catch (e) {
      throw e;
    }
  }

  purchaseOrderTaskList(
    start: string | null,
    end: string | null
  ): Promise<BoekestynTaskListResponse> {
    var startParam = start ? `&start=${start}` : '',
      endParam = end ? `&end=${end}` : '';
    return this.get(`/boekestyns/purchaseOrders?${startParam}${endParam}`);
  }

  purchaseOrderTaskReportUrl(
    id: number
  ): Promise<BoekestynTaskReportUrlResponse> {
    return this.get(`/boekestyns/purchaseOrders/${id}/url`);
  }

  setPurchaseOrderItemPriority(id: number, priority: boolean) {
    return this.put(
      `/boekestyns/purchaseOrders/items/${id}/priority?priority=${priority}`
    );
  }

  setPrebookItemPriority(id: number, priority: boolean) {
    return this.put(
      `/boekestyns/prebooks/items/${id}/priority?priority=${priority}`
    );
  }

  completePurchaseOrderItemUpc(id: number) {
    return this.put(`/boekestyns/purchaseOrders/items/${id}/upcComplete`);
  }

  completePrebookItemUpc(id: number) {
    return this.put(`/boekestyns/prebooks/items/${id}/upcComplete`);
  }

  uncompletePrebookItemUpc(id: number) {
    return this.put(`/boekestyns/prebooks/items/${id}/upcUncomplete`);
  }

  overridePrebookItemUpc(args: OverridePrebookItemUpcArgs) {
    return this.post(
      `/boekestyns/prebooks/items/${args.prebookItemId}/upcOverride`,
      args
    );
  }

  overridePrebookUpc(args: OverridePrebookUpcArgs) {
    return this.post(
      `/boekestyns/futureOrders/${args.futureOrderId}/upcOverride`,
      args
    );
  }

  startPrep(id: number) {
    return this.put(`/boekestyns/purchaseOrders/items/${id}/prepStart`);
  }

  completePrep(id: number) {
    return this.put(`/boekestyns/purchaseOrders/items/${id}/prepComplete`);
  }

  startPacking(id: number) {
    return this.put(`/boekestyns/purchaseOrders/items/${id}/packingStart`);
  }

  completePacking(id: number) {
    return this.put(`/boekestyns/purchaseOrders/items/${id}/packingComplete`);
  }
}

export const boekestynListApi = createApi({
  reducerPath: 'boekestyn-list-api',
  baseQuery: axiosBaseQuery('boekestyns/'),
  refetchOnMountOrArgChange: true,
  endpoints: (builder) => ({
    itemList: builder.query<BoekestynItemListResponse, BoekestynItemListArgs>({
      query: ({ startDate, endDate }) => ({
        url: `items?startDate=${startDate}&endDate=${endDate}`,
      }),
    }),
    itemRequirementsList: builder.query<
      BoekestynItemRequirementsListResponse,
      BoekestynItemRequirementsListArgs
    >({
      query: ({ startDate, endDate }) => ({
        url: `items/Requirements?startDate=${startDate}&endDate=${endDate}`,
      }),
    }),
    sales: builder.query<BoekestynSalesResponse, BoekestynSalesArgs>({
      query: ({ startDate, endDate }) => ({
        url: `sales?startDate=${startDate}&endDate=${endDate}`,
      }),
    }),
  }),
});

export interface BoekestynItemListDownloadArgs {
  startDate: string | null;
  endDate: string | null;
  items: models.ItemListItem[];
}

export interface BoekestynSalesDownloadArgs {
  plant: string | null;
  customers: string[];
  plants: models.Plant[];
  prebookItems: models.BoekestynPrebookItem[];
  productionOrders: models.BoekestynProductionOrder[];
}

interface BoekestynItemListArgs {
  startDate: string;
  endDate: string;
}

interface BoekestynItemListResponse {
  items: models.ItemListItem[];
}

interface BoekestynItemRequirementsListArgs {
  startDate: string;
  endDate: string;
}

interface BoekestynItemRequirementsListResponse {
  items: models.ItemListRequirementItem[];
}

interface BoekestynPlantResponse {
  plants: models.Plant[];
}

interface BoekestynCustomerResponse {
  customers: models.Customer[];
}

interface BoekestynProductionOrderResponse {
  orders: couch.OrderModel[];
}

interface BoekestynSalesArgs {
  startDate: string;
  endDate: string;
}

interface BoekestynSalesResponse {
  plants: models.Plant[];
  prebookItems: models.BoekestynPrebookItem[];
  productionOrders: models.BoekestynProductionOrder[];
}

export interface ProductionOrder {
  customer: string;
  weekNumber: number;
  year: number;
  plantId: string;
  description: string;
  cases: number;
  salesWeeks?: models.SalesWeek[];
}

export interface BoekestynTaskListResponse {
  tasks: models.BoekestynTask[];
}

export interface BoekestynTaskReportUrlResponse {
  url: string;
}

export interface OverridePrebookItemUpcArgs {
  prebookItemId: number;
  subject: string;
  body: string;
  cc: string;
}

export interface OverridePrebookUpcArgs {
  futureOrderId: number;
  subject: string;
  body: string;
  cc: string;
}

export const { useItemListQuery, useSalesQuery } = boekestynListApi;

export const boekestynApi = new BoekestynService();
