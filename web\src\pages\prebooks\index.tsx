import React, { useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { Switch } from '@headlessui/react';
import { useListQuery } from 'api/prebooks-service';
import * as models from 'api/models/prebooks';
import { usePermissions } from '@/services/auth';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import {
  clearError,
  setStartDate,
  setEndDate,
  setSearch,
  setPrebookSort,
  setVendorFilter,
  setCustomerFilter,
  setShipToFilter,
  setSalespersonFilter,
  setSeasonFilter,
  setBlanketFilter,
  selectStartDate,
  selectEndDate,
  selectSearch,
  selectPrebookSort,
  selectSortPrebooksDescending,
  selectIsLoading,
  selectError,
  selectPrebooks,
  selectPrebookVendors,
  selectPrebookCustomers,
  selectPrebookShipTos,
  selectPrebookSalespeople,
  selectPrebookSeasons,
  selectFilter,
  setIncludeSpirePurchaseOrders<PERSON>ilter,
  downloadList,
  BlanketFilter,
  selectSelectedItems,
  setSelectedItems,
  unsetSelectedItems,
} from '@/components/prebooks/prebook-list-slice';
import { EmailBatch } from '@/components/prebooks/email-batch';
import { ListItem } from '@/components/prebooks/list-item';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { Loading } from '@/components/loading';
import { classNames } from '@/utils/class-names';

export default function Prebooks() {
  const dispatch = useAppDispatch(),
    { can } = usePermissions(),
    prebooks = useAppSelector(selectPrebooks),
    vendors = useAppSelector(selectPrebookVendors),
    customers = useAppSelector(selectPrebookCustomers),
    shipTos = useAppSelector(selectPrebookShipTos),
    salespeople = useAppSelector(selectPrebookSalespeople),
    seasons = useAppSelector(selectPrebookSeasons),
    startDate = useAppSelector(selectStartDate),
    endDate = useAppSelector(selectEndDate),
    search = useAppSelector(selectSearch),
    sort = useAppSelector(selectPrebookSort),
    sortDescending = useAppSelector(selectSortPrebooksDescending),
    isLoading = useAppSelector(selectIsLoading),
    error = useAppSelector(selectError),
    filter = useAppSelector(selectFilter),
    selectedItems = useAppSelector(selectSelectedItems),
    { refetch } = useListQuery({ startDate, endDate }),
    [selectAll, setSelectAll] = useState(false),
    [showSendPrebooks, setShowSendPrebooks] = useState(false),
    [showAdvanced, setShowAdvanced] = useState(
      !!filter.vendor ||
        !!filter.customer ||
        !!filter.shipTo ||
        !!filter.salesperson ||
        !!filter.season ||
        !!filter.blanket ||
        filter.includeSpirePurchaseOrders
    ),
    readonly = !can('Sales Team');

  const handleDownloadClick = () => {
    const args = {
      items: prebooks,
    };
    dispatch(downloadList(args));
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSearch(e.target.value));
  };

  const handleStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setStartDate(e.target.value || ''));
  };

  const handleEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setEndDate(e.target.value || ''));
  };

  const handleRefreshClick = () => {
    refetch();
  };

  const handleClearError = () => {
    dispatch(clearError());
  };

  const handleColumnSort = (sortProp: keyof models.PrebookListItem) => {
    const descending = sortProp === sort ? !sortDescending : false;
    dispatch(setPrebookSort({ sort: sortProp, sortDescending: descending }));
  };

  const handleToggleAdvanced = () => {
    setShowAdvanced(!showAdvanced);
    if (showAdvanced) {
      dispatch(setVendorFilter(null));
      dispatch(setCustomerFilter(null));
      dispatch(setShipToFilter(null));
      dispatch(setSalespersonFilter(null));
      dispatch(setSeasonFilter(null));
      dispatch(setBlanketFilter(''));
      dispatch(setIncludeSpirePurchaseOrdersFilter(false));
    }
  };

  const handleIncludeSpirePurchaseOrdersChange = (include: boolean) => {
    dispatch(setIncludeSpirePurchaseOrdersFilter(include));
  };

  const handleSendPrebooksClick = () => {
    setShowSendPrebooks(true);
  };

  const handleSendPrebooksCancel = () => {
    setShowSendPrebooks(false);
  };

  const handleSendPrebooksConfirm = () => {
    setShowSendPrebooks(false);
    setSelectAll(false);
    dispatch(unsetSelectedItems(selectedItems));
    refetch();
  };

  const handleVendorFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setVendorFilter(e.target.value || null));
  };

  const handleCustomerFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setCustomerFilter(e.target.value || null));
  };

  const handleShipToFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setShipToFilter(e.target.value || null));
  };

  const handleSalespersonFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setSalespersonFilter(e.target.value || null));
  };

  const handleSeasonFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setSeasonFilter(e.target.value || null));
  };

  const handleBlanketFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setBlanketFilter(e.target.value as BlanketFilter));
  };

  const handleSelectAllChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selected = prebooks.map((p) => p.id);
    setSelectAll(e.target.checked);

    if (e.target.checked) {
      dispatch(setSelectedItems(selected));
    } else {
      dispatch(unsetSelectedItems(selected));
    }
  };

  interface HeaderButtonProps {
    text: string;
    propName: keyof models.PrebookListItem;
  }
  const HeaderButton = ({ text, propName }: HeaderButtonProps) => (
    <button
      type="button"
      className="group inline-flex"
      onClick={() => handleColumnSort(propName)}
    >
      {text}
      <span
        className={classNames(
          'ml-2 flex-none rounded text-gray-400 group-hover:visible group-focus:visible',
          sort !== propName && 'invisible'
        )}
      >
        <Icon
          icon={sortDescending ? 'chevron-down' : 'chevron-up'}
          className="h-5 w-5"
          aria-hidden="true"
        />
      </span>
    </button>
  );

  return (
    <>
      <Head>
        <title>Prebook List</title>
      </Head>
      <header className="bg-white shadow">
        <div className="grid grid-cols-1">
          <div className="mb-4 border-b shadow">
            <div className="mx-auto flex max-w-7xl items-center justify-between px-8">
              <div className="flex">
                <nav className="ml-24 flex flex-row">
                  <div className="group relative my-2 text-nowrap rounded-lg bg-blue-500 p-2 text-center text-sm font-medium text-white">
                    Prebook List
                  </div>
                  <Link
                    href={routes.prebooks.items.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Prebook Items
                  </Link>
                  <Link
                    href={routes.prebooks.blanketItems.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Blanket Prebooks
                  </Link>
                </nav>
              </div>
            </div>
          </div>
          <div className="bg-gray-100 py-2">
            <div className="mx-auto flex max-w-7xl items-center justify-between px-8">
              <div className="flex flex-grow items-center align-top">
                <div className="flex w-full flex-col rounded p-2">
                  <div className="grid w-full grid-cols-8 gap-2 rounded-sm text-xs">
                    <div>
                      <label htmlFor="start-date">From Date</label>
                      <input
                        type="date"
                        max="2050-01-01"
                        id="start-date"
                        value={startDate}
                        onChange={handleStartDateChange}
                        className="w-full !max-w-none text-xs"
                      />
                    </div>
                    <div>
                      <label htmlFor="end-date">To Date</label>
                      <input
                        type="date"
                        max="2050-01-01"
                        id="end-date"
                        value={endDate}
                        onChange={handleEndDateChange}
                        className="w-full !max-w-none text-xs"
                      />
                    </div>
                    <div className="col-span-4">
                      <label htmlFor="end-date">Search</label>
                      <div className="flex">
                        <input
                          type="search"
                          id="search"
                          name="search"
                          value={search}
                          onChange={handleSearchChange}
                          className="flex-grow text-xs"
                          placeholder="Search"
                          autoComplete="off"
                        />
                        <button
                          type="button"
                          className="btn-secondary flex px-1 focus:ring-0"
                          onClick={handleToggleAdvanced}
                        >
                          <Icon
                            icon={showAdvanced ? 'chevron-up' : 'chevron-down'}
                            className="h-5 w-5"
                          />
                        </button>
                      </div>
                    </div>
                    <div className="flex items-start justify-center pt-3">
                      <button
                        type="button"
                        onClick={handleRefreshClick}
                        className="btn-secondary flex p-3 text-blue-700 "
                      >
                        <Icon icon="refresh" spin={isLoading} />
                      </button>
                      <button
                        type="button"
                        onClick={handleDownloadClick}
                        className="btn-secondary flex p-3 text-green-700"
                      >
                        <Icon icon="file-excel" />
                      </button>
                    </div>
                    {!readonly && (
                      <div className="flex items-start justify-center pt-3">
                        <Link
                          href={routes.prebooks.new.to()}
                          className="btn-new flex flex-nowrap"
                        >
                          <Icon icon="plus-circle" className="flex" />
                          &nbsp;
                          <div className="flex whitespace-nowrap">
                            New Prebook
                          </div>
                        </Link>
                      </div>
                    )}
                    {showAdvanced && (
                      <>
                        <div className="col-start-1">
                          <label htmlFor="blanket-filter">Blanket</label>
                          <select
                            id="blanket-filter"
                            value={filter.blanket}
                            onChange={handleBlanketFilterChange}
                            className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                          >
                            <option value="">All</option>
                            <option value="Blanket">Blankets</option>
                            <option value="Non-Blanket">Non-Blankets</option>
                          </select>
                        </div>
                        <div>
                          <label htmlFor="vendor-filter">Vendor</label>
                          <select
                            id="vendor-filter"
                            value={filter.vendor || ''}
                            onChange={handleVendorFilterChange}
                            className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                          >
                            <option value="">All Vendors</option>
                            {vendors.map((vendor) => (
                              <option key={vendor}>{vendor}</option>
                            ))}
                          </select>
                        </div>
                        <div>
                          <label htmlFor="customer-filter">Customer</label>
                          <select
                            id="customer-filter"
                            value={filter.customer || ''}
                            onChange={handleCustomerFilterChange}
                            className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                          >
                            <option value="">All Customers</option>
                            {customers.map((customer) => (
                              <option key={customer}>{customer}</option>
                            ))}
                          </select>
                        </div>
                        <div className="">
                          <label htmlFor="ship-to-filter">Ship To</label>
                          <select
                            id="ship-to-filter"
                            value={filter.shipTo || ''}
                            onChange={handleShipToFilterChange}
                            className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                          >
                            <option value="">All Ship Tos</option>
                            {shipTos.map((shipTo) => (
                              <option key={shipTo}>{shipTo}</option>
                            ))}
                          </select>
                        </div>
                        <div className="">
                          <label htmlFor="salesperson-filter">
                            Salesperson
                          </label>
                          <select
                            id="salesperson-filter"
                            value={filter.salesperson || ''}
                            onChange={handleSalespersonFilterChange}
                            className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                          >
                            <option value="">All Salespeople</option>
                            {salespeople.map((salesperson) => (
                              <option key={salesperson}>{salesperson}</option>
                            ))}
                          </select>
                        </div>
                        <div>
                          <label htmlFor="season-filter">Season</label>
                          <select
                            id="season-filter"
                            value={filter.season || ''}
                            onChange={handleSeasonFilterChange}
                            className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                          >
                            <option value="">All Seasons</option>
                            {seasons.map((season) => (
                              <option key={season}>{season}</option>
                            ))}
                          </select>
                        </div>
                        <div>
                          <label
                            htmlFor="vendor-filter"
                            className="block w-full text-center"
                          >
                            Include Spire POs
                          </label>
                          <div className="mt-3 text-center align-middle">
                            <Switch
                              checked={filter.includeSpirePurchaseOrders}
                              onChange={handleIncludeSpirePurchaseOrdersChange}
                              className={`${
                                filter.includeSpirePurchaseOrders
                                  ? 'bg-blue-600'
                                  : 'bg-gray-200'
                              } relative inline-flex h-6 w-11 items-center rounded-full`}
                            >
                              <span className="sr-only">Include Spire POs</span>
                              <span
                                className={`${
                                  filter.includeSpirePurchaseOrders
                                    ? 'translate-x-6'
                                    : 'translate-x-1'
                                } inline-block h-4 w-4 transform rounded-full bg-white transition`}
                              />
                            </Switch>
                          </div>
                        </div>
                        {!!filter.vendor && !!selectedItems.length && (
                          <div>
                            <button
                              type="button"
                              onClick={handleSendPrebooksClick}
                              className="btn-primary mt-2 flex p-3"
                            >
                              Send Prebooks&nbsp;
                              <Icon icon="paper-plane" />
                            </button>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>
      <main className="flex-grow overflow-auto">
        <div className="mx-auto h-full px-8">
          <Error error={error} clear={handleClearError} />
          {isLoading && <Loading />}
          <div className="mt-8 flex h-full flex-col">
            <div className="-mx-8 -my-2 h-full">
              <div className="inline-block min-w-full px-8 py-2 align-middle">
                <div className="rounded-lg shadow ring-1 ring-black ring-opacity-5">
                  <table className="min-w-full divide-y divide-gray-300">
                    <thead>
                      <tr className="sticky top-0 z-10">
                        <th
                          scope="col"
                          className="bg-gray-100 px-2 py-3.5 text-center font-semibold text-gray-900"
                        >
                          {!!filter.vendor && (
                            <input
                              type="checkbox"
                              checked={selectAll}
                              onChange={handleSelectAllChange}
                            />
                          )}
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-2 py-3.5 font-semibold text-gray-900"
                        >
                          <HeaderButton text="Id" propName="id" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton text="Date" propName="requiredDate" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton text="Vendor" propName="vendor" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton text="Customer" propName="customer" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton
                            text="Salesperson"
                            propName="salesperson"
                          />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton text="Season" propName="season" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton text="Cases" propName="caseCount" />
                        </th>
                        {filter.includeSpirePurchaseOrders && (
                          <th
                            scope="col"
                            className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                          >
                            <HeaderButton
                              text="Spire PO #"
                              propName="spirePurchaseOrderNumber"
                            />
                          </th>
                        )}
                      </tr>
                    </thead>
                    <tbody className="bg-white">
                      {prebooks.map((prebook) => (
                        <ListItem
                          key={prebook.id}
                          prebook={prebook}
                          refresh={refetch}
                        />
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
        <EmailBatch
          open={showSendPrebooks}
          cancel={handleSendPrebooksCancel}
          close={handleSendPrebooksConfirm}
        />
      </main>
    </>
  );
}
