import { useState, useCallback, useEffect, useMemo } from 'react';
import { DateTime, Duration } from 'luxon';
import {
  useStartHarvestingWorkOrderLabourMutation,
  usePauseHarvestingWorkOrderLabourMutation,
  useStopHarvestingWorkOrderLabourMutation,
  HarvestingLabourVariety,
} from 'api/boekestyn-harvesting-service';
import * as boeks from 'api/models/boekestyns';
import { Icon } from '@/components/icon';
import { useAppSelector } from '@/services/hooks';
import { selectWorkOrders } from './harvesting-slice';
import { RecordLabourDialog } from './record-labour-dialog';
import { StartLabourDialog } from './start-labour-dialog';
import { formatNumber } from '@/utils/format';

interface TimingProps {
  workOrder: boeks.HarvestingWorkOrderItem;
}

export function Timing({ workOrder }: TimingProps) {
  const labour = useMemo(() => workOrder.labour ?? [], [workOrder]),
    workOrders = useAppSelector(selectWorkOrders),
    inProcess = useMemo(() => labour.find((l) => !l.endTime) || null, [labour]),
    isFinalLabour = useMemo(() => labour.some((l) => l.finalLabour), [labour]),
    [startLabour] = useStartHarvestingWorkOrderLabourMutation(),
    [pauseLabour] = usePauseHarvestingWorkOrderLabourMutation(),
    [stopLabour] = useStopHarvestingWorkOrderLabourMutation(),
    [ellapsed, setEllapsed] = useState(''),
    [timer, setTimer] = useState(0),
    [showStartDialog, setShowStartDialog] = useState(false),
    [showRecordDialog, setShowRecordDialog] = useState(false),
    isFirstAvailableLabour = useMemo(() => {
      const index = workOrders.findIndex((o) => o.id === workOrder.id),
        firstEligibleLabour = workOrders.find((o, i) => {
          return (
            i < index &&
            (!o.labour.length || o.labour.every((l) => !l.finalLabour))
          );
        });
      return !firstEligibleLabour;
    }, [workOrder.id, workOrders]),
    previousDaysLabour = useMemo(() => {
      if (!workOrder.previousDaysLabourMinutes) {
        return null;
      }

      return formatNumber(workOrder.previousDaysLabourMinutes) + ' minutes';
    }, [workOrder.previousDaysLabourMinutes]);

  const update = useCallback(() => {
    if (labour.length) {
      const ellapsedMillis = Math.ceil(
          labour.reduce((memo, l) => {
            if (!l.startTime) {
              return memo;
            }

            const started = DateTime.fromFormat(
                l.startTime,
                'yyyy-MM-dd HH:mm:ss'
              ).toMillis(),
              ended = l.endTime
                ? DateTime.fromFormat(
                    l.endTime,
                    'yyyy-MM-dd HH:mm:ss'
                  ).toMillis()
                : Date.now().valueOf(),
              diff = ended - started;
            return memo + diff;
          }, 0)
        ),
        previousDaysMillis = (workOrder.previousDaysLabourMinutes ?? 0) * 60000,
        todaysMillis = Math.max(0, ellapsedMillis - previousDaysMillis),
        duration = Duration.fromMillis(todaysMillis),
        ellapsed = todaysMillis
          ? todaysMillis < 60000
            ? `${duration.toFormat('s')} seconds`
            : `${duration.toFormat('m')} minutes`
          : '';

      setEllapsed(ellapsed);
    } else {
      setEllapsed('');
    }
  }, [labour, workOrder.previousDaysLabourMinutes]);

  useEffect(() => {
    if (timer) {
      window.clearInterval(timer);
    }

    const newTimer = window.setInterval(update, 1000);
    setTimer(newTimer);

    return () => {
      window.clearInterval(newTimer);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [update]);

  const handleStartClick = () => {
    setShowStartDialog(true);
  };

  const handleStopClick = async (args: {
    comments: string | null;
    crewSize: number;
    varieties: HarvestingLabourVariety[];
    finalHarvest: boolean;
  }) => {
    if (inProcess) {
      if (timer) {
        window.clearInterval(timer);
      }

      stopLabour({
        workOrderId: workOrder.id,
        crewSize: args.crewSize,
        comments: args.comments,
        labourVarieties: args.varieties,
        harvestComplete: args.finalHarvest,
      });
    }
  };

  const openPauseDialog = async () => {
    if (inProcess) {
      if (timer) {
        window.clearInterval(timer);
      }
      setShowRecordDialog(true);
    }
  };

  const handlePauseClick = async ({
    comments,
  }: {
    comments: string | null;
  }) => {
    if (inProcess) {
      pauseLabour({
        workOrderId: workOrder.id,
        crewSize: inProcess.crewSize,
        comments,
      });
    }

    setShowRecordDialog(false);
  };

  const handleRecordClick = async ({
    comments,
  }: {
    comments: string | null;
  }) => {
    if (inProcess) {
      pauseLabour({
        workOrderId: workOrder.id,
        crewSize: inProcess.crewSize,
        comments,
      });
    }

    setShowRecordDialog(false);
  };

  return (
    <>
      {isFirstAvailableLabour && !inProcess && !isFinalLabour && (
        <button
          type="button"
          className="btn-secondary"
          onClick={handleStartClick}
        >
          <Icon icon="play" />
        </button>
      )}
      {isFirstAvailableLabour && !!inProcess && (
        <>
          <button
            type="button"
            className="btn-secondary"
            onClick={openPauseDialog}
          >
            <Icon icon="calculator" />
          </button>
          <button
            type="button"
            className="btn-secondary ml-2"
            onClick={() => handlePauseClick({ comments: '' })}
          >
            <Icon icon="pause" />
          </button>
        </>
      )}
      {!!previousDaysLabour && (
        <div className="text-sm font-semibold italic text-zinc-500">
          Yesterday: {previousDaysLabour}
        </div>
      )}
      {!!ellapsed && <p>{ellapsed}</p>}
      {!!inProcess && <div className="italic text-zinc-500">Running</div>}
      {!inProcess && !!ellapsed && !isFinalLabour && (
        <div className="italic text-zinc-500">Paused</div>
      )}
      {isFinalLabour && <div className="font-light italic">Finished</div>}

      <RecordLabourDialog
        open={showRecordDialog}
        workOrder={workOrder}
        onClose={() => setShowRecordDialog(false)}
        onRecord={handleStopClick}
      />
      <StartLabourDialog
        open={showStartDialog}
        onClose={() => setShowStartDialog(false)}
        onStart={() => {
          setShowStartDialog(false);
          startLabour({
            workOrderId: workOrder.id,
            crewSize: workOrder.crewSize,
          });
        }}
      />
    </>
  );
}
