import { AxiosError, AxiosResponseHeaders, ResponseType } from 'axios';
import { serializeError } from 'serialize-error';
import { createApi } from '@reduxjs/toolkit/query/react';
import axios, {
  ApiBase,
  axiosBaseQuery,
  getFilename,
  downloadFile,
} from './api-base';
import * as models from './models/prebooks';
import * as spire from './models/spire';
import {
  createProblemDetails,
  isProblemDetails,
} from '@/utils/problem-details';
import { UpgradeItemAttachment } from './models/future-orders';

const type =
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  responseType: ResponseType = 'blob';

class PrebooksService extends ApiBase {
  list({ startDate, endDate }: PrebookListArgs): Promise<PrebookListResponse> {
    return this.get(`prebooks?startDate=${startDate}&endDate=${endDate}`);
  }

  detail(id: number): Promise<PrebookDetailResponse> {
    return this.get(`prebooks/${id}`);
  }

  create(data: models.PrebookCreate): Promise<PrebookCreateResponse> {
    return this.post('prebooks', data);
  }

  update(data: models.PrebookDetail): Promise<PrebookDetailResponse> {
    return this.put(`prebooks/${data.id}`, data);
  }

  remove(id: number): Promise<void> {
    return this.delete(`prebooks/${id}`);
  }

  confirm(id: number): Promise<ConfirmPrebookResponse> {
    return this.post(`prebooks/${id}/confirm`);
  }

  sendToSpire(id: number): Promise<void> {
    return this.post(`prebooks/${id}/spire`);
  }

  confirmUpgradeItem(id: number) {
    return this.post(`prebooks/items/${id}/upgrades/confirm`);
  }

  productShipToDefaults(
    spireInventoryId: number,
    customerId: number,
    shipToId: number
  ): Promise<ProductShipToResponse> {
    return this.get(
      `prebooks/defaults?spireInventoryId=${spireInventoryId}&customerId=${customerId}&shipToId=${shipToId}`
    );
  }

  blanketItems(): Promise<BlanketItemsResponse> {
    return this.get('prebooks/blanketItems');
  }

  pendingEmail(id: number): Promise<PendingEmailResponse> {
    return this.get(`prebooks/${id}/email/pending`);
  }

  pendingBatchEmail(vendorId: number): Promise<PendingBatchEmailResponse> {
    return this.get(`prebooks/email/pending/${vendorId}`);
  }

  createEmail(
    id: number,
    to: string,
    cc: string | null,
    bcc: string | null,
    subject: string,
    body: string,
    upgradesBody: string,
    upgradesCc: string | null
  ): Promise<CreateEmailResponse> {
    const data = { to, cc, bcc, subject, body, upgradesBody, upgradesCc };
    return this.post(`prebooks/${id}/email`, data);
  }

  createBatchEmail(
    prebookIds: number[],
    to: string,
    cc: string | null,
    bcc: string | null,
    subject: string,
    body: string
  ): Promise<void> {
    const data = { to, cc, bcc, subject, body, prebookIds };
    return this.post(`prebooks/emails`, data);
  }

  async prebookListDownload(data: PrebookListDownloadArgs) {
    try {
      const { headers, data: result } = await axios.post(
          '/prebooks/download',
          data,
          {
            responseType,
          }
        ),
        blob = new Blob([result], { type }),
        filename =
          getFilename(headers as AxiosResponseHeaders) || 'Prebooks.xlsx';

      downloadFile(blob, filename);
    } catch (ex) {
      if (ex instanceof Error) {
        const error = serializeError(ex);
        throw createProblemDetails(error.message || 'Error');
      }
      const e = ex as AxiosError;
      if (e.response) {
        if (isProblemDetails(e.response.data)) {
          throw e.response.data;
        } else if (typeof e.response.data === 'string') {
          throw createProblemDetails(e.response.data);
        }
      }

      if (e.response && e.response.data) {
        throw e.response.data;
      }

      throw e.message;
    }
  }

  async itemSummaryDownload(data: ItemSummaryDownloadArgs) {
    try {
      const { headers, data: result } = await axios.post(
          '/prebooks/items/download',
          data,
          {
            responseType,
          }
        ),
        blob = new Blob([result], { type }),
        filename =
          getFilename(headers as AxiosResponseHeaders) || 'PrebookItems.xlsx';

      downloadFile(blob, filename);
    } catch (ex) {
      if (ex instanceof Error) {
        const error = serializeError(ex);
        throw createProblemDetails(error.message || 'Error');
      }
      const e = ex as AxiosError;
      if (e.response) {
        if (isProblemDetails(e.response.data)) {
          throw e.response.data;
        } else if (typeof e.response.data === 'string') {
          throw createProblemDetails(e.response.data);
        }
      }

      if (e.response && e.response.data) {
        throw e.response.data;
      }

      throw e.message;
    }
  }

  async blanketItemDownload(data: BlanketItemDownloadArgs) {
    try {
      const { headers, data: result } = await axios.post(
          '/prebooks/items/blanket/download',
          data,
          {
            responseType,
          }
        ),
        blob = new Blob([result], { type }),
        filename =
          getFilename(headers as AxiosResponseHeaders) || 'BlanketItems.xlsx';

      downloadFile(blob, filename);
    } catch (ex) {
      if (ex instanceof Error) {
        const error = serializeError(ex);
        throw createProblemDetails(error.message || 'Error');
      }
      const e = ex as AxiosError;
      if (e.response) {
        if (isProblemDetails(e.response.data)) {
          throw e.response.data;
        } else if (typeof e.response.data === 'string') {
          throw createProblemDetails(e.response.data);
        }
      }

      if (e.response && e.response.data) {
        throw e.response.data;
      }

      throw e.message;
    }
  }

  async overAndAboveDownload(data: OverAndAboveDownloadArgs) {
    try {
      const { headers, data: result } = await axios.post(
          '/prebooks/over-and-above',
          data,
          {
            responseType,
          }
        ),
        blob = new Blob([result], { type }),
        filename =
          getFilename(headers as AxiosResponseHeaders) || 'OverAndAbove.xlsx';

      downloadFile(blob, filename);
    } catch (ex) {
      if (ex instanceof Error) {
        const error = serializeError(ex);
        throw createProblemDetails(error.message || 'Error');
      }
      const e = ex as AxiosError;
      if (e.response) {
        if (isProblemDetails(e.response.data)) {
          throw e.response.data;
        } else if (typeof e.response.data === 'string') {
          throw createProblemDetails(e.response.data);
        }
      }

      if (e.response && e.response.data) {
        throw e.response.data;
      }

      throw e.message;
    }
  }

  async attachments(id: number): Promise<PrebookAttachmentsResponse> {
    const { data } = await axios.get(`/prebooks/attachments/${id}`);
    return data;
  }

  async addUpgradeAttachment({ file, prebookItemIds }: AddAttachmentModel) {
    const data = new FormData();
    data.append('file', file);
    data.append('fileType', file.type);
    prebookItemIds.forEach((i) => data.append('prebookItemIds', i.toString()));
    return await axios.post<AddAttachmentResponse>(
      '/prebooks/attachments',
      data
    );
  }

  async deleteUpgradeAttachment(id: number) {
    return await axios.delete(`/prebooks/attachments/${id}`);
  }
}

export const prebooksApi = new PrebooksService();

export const prebookListApi = createApi({
  reducerPath: 'prebook-list-api',
  baseQuery: axiosBaseQuery('prebooks/'),
  refetchOnMountOrArgChange: true,
  endpoints: (builder) => ({
    list: builder.query<PrebookListResponse, PrebookListArgs>({
      query: ({ startDate, endDate }) => ({
        url: `?startDate=${startDate}&endDate=${endDate}`,
      }),
    }),
    itemSummary: builder.query<ItemSummaryResponse, ItemSummaryArgs>({
      query: ({
        startDate,
        endDate,
        groupByCustomer,
        groupByShipTo,
        groupByVendor,
        groupBySalesperson,
        groupBySeason,
        groupByIsBlanket,
        dateGrouping,
      }) => ({
        url:
          `items?startDate=${startDate}&endDate=${endDate}&groupByCustomer=${groupByCustomer}&` +
          `groupByShipTo=${groupByShipTo}&groupByVendor=${groupByVendor}&groupBySalesperson=${groupBySalesperson}&` +
          `groupBySeason=${groupBySeason}&groupByIsBlanket=${groupByIsBlanket}&dateGrouping=${dateGrouping}`,
      }),
    }),
    blanketItems: builder.query<BlanketItemListResponse, BlanketItemListArgs>({
      query: ({ startDate, endDate }) => ({
        url: `items/blanket?startDate=${startDate}&endDate=${endDate}`,
      }),
    }),
    openBlanketItems: builder.query<BlanketItemsResponse, void>({
      query: () => ({
        url: 'blanketItems',
      }),
    }),
  }),
});

export const seasonsApi = createApi({
  reducerPath: 'seasons-api',
  tagTypes: ['Seasons'],
  baseQuery: axiosBaseQuery('prebooks/seasons'),
  endpoints: (builder) => ({
    seasons: builder.query<models.Season[], void>({
      query: () => ({
        url: '',
      }),
      transformResponse: ({ seasons }: SeasonsResponse) => seasons,
      providesTags: ['Seasons'],
    }),
    addSeason: builder.mutation<void, CreateSeasonModel>({
      query: (data) => ({ url: '', method: 'POST', data }),
      invalidatesTags: ['Seasons'],
    }),
  }),
});

export const { useSeasonsQuery, useAddSeasonMutation } = seasonsApi;
export const {
  useListQuery,
  useItemSummaryQuery,
  useBlanketItemsQuery,
  useOpenBlanketItemsQuery,
} = prebookListApi;

export interface PrebookListArgs {
  startDate: string;
  endDate: string;
}

export interface PrebookListResponse {
  prebooks: models.PrebookListItem[];
}

export type DateGrouping = 'Date' | 'Week' | 'Month' | 'Year' | 'None';

export interface PrebookListDownloadArgs {
  items: models.PrebookListItem[];
}

export interface ItemSummaryArgs {
  startDate: string | null;
  endDate: string | null;
  groupByCustomer: boolean;
  groupByShipTo: boolean;
  groupByVendor: boolean;
  groupBySalesperson: boolean;
  groupBySeason: boolean;
  groupByIsBlanket: boolean;
  dateGrouping: DateGrouping;
}

export interface ItemSummaryDownloadArgs extends ItemSummaryArgs {
  items: models.PrebookSummaryItem[];
}

export interface BlanketItemDownloadArgs {
  blanketItems: models.BlanketItemListItem[];
}

export interface OverAndAboveDownloadArgs {
  startDate: string | null;
  endDate: string | null;
}

export interface ItemSummaryResponse {
  items: models.PrebookSummaryItem[];
}

export interface BlanketItemListArgs {
  startDate: string;
  endDate: string;
}

export interface BlanketItemListResponse {
  items: models.BlanketItemListItem[];
}

export interface PrebookCreateResponse {
  id: number;
}

export interface PrebookDetailResponse {
  prebook: models.PrebookDetail;
  emails: models.PrebookEmail[];
  blanketItems: models.PrebookBlanketItem[];
}

export interface CreateSeasonModel {
  name: string;
  seasonDate: string;
}

export interface SeasonsResponse {
  seasons: models.Season[];
}

export interface ProductShipToResponse {
  default: models.ProductShipToDefault;
}

export interface BlanketItemsResponse {
  blanketItems: models.PrebookBlanketItem[];
}

export interface PendingEmailResponse {
  prebook: models.PendingPrebookEmail;
  toAddresses: string[];
  templates: spire.EmailTemplate[];
}

export interface PendingBatchEmailResponse {
  toAddresses: string[];
}

export interface CreateEmailResponse {
  email: models.PrebookEmail;
}

export interface ConfirmPrebookResponse {
  prebook: models.PrebookDetail;
}

export interface AddAttachmentModel {
  file: File;
  prebookItemIds: number[];
}

export interface AddAttachmentResponse {
  attachments: UpgradeItemAttachment[];
}

export interface PrebookAttachmentsResponse {
  attachments: UpgradeItemAttachment[];
}
