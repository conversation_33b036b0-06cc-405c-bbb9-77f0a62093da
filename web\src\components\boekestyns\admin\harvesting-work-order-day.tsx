import moment from 'moment';
import { useState } from 'react';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { handleFocus } from '@/utils/focus';
import {
  ScheduleData,
  setVarietyExpectedPercentages,
  setDefaultExpectedHarvestPercentage as setDefaultExpectedHarvestPercentageAction,
  setCrewSize,
  setComments,
  selectSelectedOrder,
  setScheduleDate,
} from './harvesting-work-orders-slice';
import { HarvestingWorkOrderVarietyRow } from './harvesting-work-order-variety';

export type HarvestingWorkOrderDayProps = {
  schedule: ScheduleData;
};

export function HarvestingWorkOrderDay({
  schedule,
}: HarvestingWorkOrderDayProps) {
  const dispatch = useAppDispatch(),
    order = useAppSelector(selectSelectedOrder),
    workOrder = schedule.workOrder,
    crewSize = workOrder.crewSize || 1,
    comments = workOrder.harvestingComments || null,
    [defaultExpectedHarvestPercentage, setDefaultExpectedHarvestPercentage] =
      useState<number>(workOrder.defaultExpectedHarvestPercentage);

  const updateCrewSize = (crewSize: number) => {
    dispatch(setCrewSize({ id: schedule.id, crewSize }));
  };

  const updateComments = (comments: string) => {
    dispatch(setComments({ id: schedule.id, comments }));
  };

  const handleApplyDefaultPercentage = () => {
    dispatch(
      setVarietyExpectedPercentages({
        workOrderId: schedule.workOrder.id,
        percentage: defaultExpectedHarvestPercentage,
      })
    );
  };

  const handleDefaultExpectedHarvestPercentageBlur = () => {
    dispatch(
      setDefaultExpectedHarvestPercentageAction({
        workOrderId: schedule.workOrder.id,
        percentage: defaultExpectedHarvestPercentage,
      })
    );
  };

  const handleScheduleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newDate = e.target.value;
    dispatch(
      setScheduleDate({
        scheduleId: schedule.id,
        date: newDate,
      })
    );
  };

  return (
    <div className="p-4">
      <div className="mb-4 flex items-center justify-between">
        <h3 className="text-lg font-semibold">
          Work Order for {moment(schedule.date).format('dddd, MMMM D, YYYY')}
        </h3>
        {schedule.workOrder.finalRound && (
          <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
            Final Round
          </span>
        )}
      </div>

      <div className="rounded border p-8">
        <div className="flex justify-between">
          <h3 className="text-lg font-semibold text-gray-900">
            {order?.orderNumber}
            <div className="italic">
              {order?.plantSize} {order?.plantCrop}
            </div>
          </h3>
          <div>
            <label
              htmlFor="schedule"
              className="block text-sm font-medium text-gray-500"
            >
              Date
            </label>
            <input
              type="date"
              id="schedule-date"
              name="schedule-date"
              value={schedule.date}
              onChange={handleScheduleDateChange}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          </div>
          {!schedule.workOrder.finalRound && (
            <div className="flex items-center">
              <label
                htmlFor="defaultPercentage"
                className="mr-2 text-sm font-medium text-gray-700"
              >
                Default Percentage:
              </label>
              <input
                type="number"
                min="0"
                max="100"
                value={defaultExpectedHarvestPercentage}
                onChange={(e) =>
                  setDefaultExpectedHarvestPercentage(e.target.valueAsNumber)
                }
                onBlur={handleDefaultExpectedHarvestPercentageBlur}
                onFocus={handleFocus}
                className="block w-24 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
              <button
                onClick={handleApplyDefaultPercentage}
                className="btn-secondary ml-2 "
              >
                Apply to all
              </button>
            </div>
          )}
        </div>

        <div>
          <table className="min-w-full divide-y divide-gray-300 text-sm">
            <thead>
              <tr>
                <th className="p-2">Variety</th>
                <th className="w-1 p-2 text-right">Total Planted</th>
                <th className="w-1 p-2 text-right">Already Scheduled</th>
                <th className="w-1 p-2 text-right">Available</th>
                <th className="w-1 p-2 text-right">Expected Percentage</th>
                <th className="w-1 whitespace-nowrap p-2 text-right">
                  To Schedule
                </th>
                <th>&nbsp;</th>
              </tr>
            </thead>
            <tbody>
              {workOrder.varieties.map((variety) => (
                <HarvestingWorkOrderVarietyRow
                  key={variety.name}
                  schedule={schedule}
                  workOrder={workOrder}
                  variety={variety}
                />
              ))}
            </tbody>
          </table>
        </div>
        <div className="mt-4 grid grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-500">
              Crew Size
            </label>
            <input
              type="number"
              value={crewSize}
              onChange={(e) => updateCrewSize(e.target.valueAsNumber)}
              onFocus={handleFocus}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          </div>
          <div className="col-span-3">
            <label className="block text-sm font-medium text-gray-500">
              Comments
            </label>
            <textarea
              rows={3}
              value={comments ?? ''}
              onChange={(e) => updateComments(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
