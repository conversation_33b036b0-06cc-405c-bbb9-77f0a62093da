import { createApi } from '@reduxjs/toolkit/query/react';
import { ApiBase, axiosBaseQuery } from './api-base';
import * as futureOrders from './models/future-orders';
import * as prebooks from './models/prebooks';
import * as settings from './models/settings';
import * as spire from './models/spire';

class SettingsService extends ApiBase {
  async customerSettings(id: number): Promise<CustomerSettingsResponse> {
    return this.get(`settings/customers/${id}`);
  }

  defaults(
    id: number,
    data: CustomerDefaultsModel
  ): Promise<CustomerSettingsResponse> {
    return this.put(`settings/customers/${id}/defaults`, data);
  }

  deleteProductCustomerDefault(id: number): Promise<void> {
    return this.delete(`settings/product-customer-defaults/${id}`);
  }

  updateProductCustomerDefault({
    id,
    customerItemCode,
  }: UpdateProductCustomerDefaultModel): Promise<UpdateProductCustomerDefaultResponse> {
    return this.put(`settings/product-customer-defaults/${id}`, {
      customerItemCode,
    });
  }

  deleteProductShipToDefault(id: number): Promise<void> {
    return this.delete(`settings/product-ship-to-defaults/${id}`);
  }

  updateProductShipToDefault(
    model: UpdateProductShipToDefaultModel
  ): Promise<UpdateProductShipToDefaultResponse> {
    return this.put(`settings/product-ship-to-defaults/${model.id}`, model);
  }

  addPotCover(customerId: number, potCover: string) {
    return this.post(`settings/customers/${customerId}/pot-covers/${potCover}`);
  }

  deletePotCover(customerId: number, potCover: string) {
    return this.delete(
      `settings/customers/${customerId}/pot-covers/${potCover}`
    );
  }

  async getAllUpgradeOptions(): Promise<settings.UpgradeOption[]> {
    const { upgradeOptions } = await this.get<UpgradeOptionsResponse>(
      'settings/upgrade-options'
    );
    return upgradeOptions;
  }

  createOrUpdateUpgradeOption(
    containerPickDescription: string,
    origins: string | null,
    costs: string | null
  ): Promise<UpdateUpgradeOptionResponse> {
    return this.post('settings/upgrade-options', {
      containerPickDescription,
      origins,
      costs,
    });
  }

  updateUpgradeOption(
    data: settings.UpgradeOption
  ): Promise<UpdateUpgradeOptionResponse> {
    return this.put(`settings/upgrade-options/${data.id}`, data);
  }

  deleteUpgradeOption(id: number): Promise<UpdateUpgradeOptionResponse> {
    return this.delete(`settings/upgrade-options/${id}`);
  }

  updateUpgradeLabourHours(
    spireInventoryId: number,
    upgradeLabourHours: number
  ) {
    return this.post('settings/product-defaults/labour-hours', {
      spireInventoryId,
      upgradeLabourHours,
    });
  }

  getAllSeasons(): Promise<SeasonListResponse> {
    return this.get('prebooks/seasons');
  }

  updateSeason(season: prebooks.Season) {
    return this.post('settings/seasons', season);
  }

  updateProductDefault(productDefault: UpdateProductDefaultModel) {
    return this.post('settings/product-defaults', productDefault);
  }

  async refreshSpireItems(
    date: string | null
  ): Promise<settings.SpireRefreshItem> {
    const query = date ? `?refreshDate=${date}` : '';
    const { refreshed } = await this.post<SpireRefreshResponse>(
      `settings/refresh-spire${query}`
    );
    return refreshed;
  }
}

export const settingsApi = createApi({
  reducerPath: 'settings-api',
  baseQuery: axiosBaseQuery('settings/'),
  refetchOnMountOrArgChange: true,
  tagTypes: ['vendor-defaults', 'freight-rates'],
  endpoints: (builder) => ({
    priceDeviationWarnings: builder.query<
      settings.PriceDeviationWarning[],
      void
    >({
      query: () => ({ url: 'price-deviation-warnings' }),
      transformResponse: ({
        priceDeviationWarnings,
      }: PriceDeviationWarningResponse) => priceDeviationWarnings,
    }),
    defaultVendorOverrides: builder.query<
      settings.DefaultVendorOverride[],
      void
    >({
      query: () => ({ url: 'default-vendor-overrides' }),
      transformResponse: ({ overrides }: DefaultVendorOverrideResponse) =>
        overrides,
      providesTags: ['vendor-defaults'],
    }),
    createDefaultVendorOverride: builder.mutation<
      void,
      settings.DefaultVendorOverride
    >({
      query: (defaultVendorOverride) => ({
        url: 'default-vendor-overrides',
        method: 'POST',
        data: { defaultVendorOverride },
      }),
      invalidatesTags: ['vendor-defaults'],
    }),
    updateDefaultVendorOverride: builder.mutation<
      void,
      settings.DefaultVendorOverride
    >({
      query: (defaultVendorOverride) => ({
        url: `default-vendor-overrides/${defaultVendorOverride.id}`,
        method: 'PUT',
        data: { defaultVendorOverride },
      }),
      invalidatesTags: ['vendor-defaults'],
    }),
    deleteDefaultVendorOverride: builder.mutation<void, number>({
      query: (id) => ({
        url: `default-vendor-overrides/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['vendor-defaults'],
    }),
    updateShipToDefault: builder.mutation<void, UpdateShipToDefaultArgs>({
      query: (data) => ({
        url: 'ship-to-defaults',
        method: 'POST',
        data,
      }),
    }),
    getFreightRates: builder.query<GetFreightRatesResponse, void>({
      query: () => ({ url: 'freight-rates' }),
      providesTags: ['freight-rates'],
    }),
    updateFreightRate: builder.mutation<void, UpdateFreightRateArgs>({
      query: (data) => ({
        url: 'freight-rates',
        method: 'POST',
        data,
      }),
      invalidatesTags: ['freight-rates'],
    }),
  }),
});

export interface CustomerSettingsResponse {
  customer: spire.CustomerDetail;
  customerItemCodeByShipTo: boolean;
  shipToDefaults: settings.ShipToDefault[];
  priceLevelFreightRates: settings.PriceLevelFreightRate[];
  productCustomerDefaults: futureOrders.ProductCustomerDefault[];
  productShipToDefaults: prebooks.ProductShipToDefault[];
  potCovers: string[];
}

export interface CustomerDefaultsModel {
  customerItemCodeByShipTo: boolean;
}

export interface UpdateProductCustomerDefaultModel {
  id: number;
  customerItemCode: string;
}

export interface UpdateProductCustomerDefaultResponse {
  productCustomerDefault: futureOrders.ProductCustomerDefault;
}

export interface UpdateProductShipToDefaultModel {
  id: number;
  hasPotCover: boolean;
  potCover: string | null;
  upc: string | null;
  weightsAndMeasures: boolean;
  retail: string | null;
  unitPrice: number | null;
  customerItemCode: string | null;
}

export interface UpdateProductShipToDefaultResponse {
  productShipToDefault: prebooks.ProductShipToDefault;
}

export interface UpdateUpgradeOptionResponse {
  upgradeOptions: settings.UpgradeOption[];
}

export interface UpgradeOptionsResponse {
  upgradeOptions: settings.UpgradeOption[];
}

export interface SeasonListResponse {
  seasons: prebooks.Season[];
}

export interface SpireRefreshResponse {
  refreshed: settings.SpireRefreshItem;
}

export interface UpdateProductDefaultModel {
  spireInventoryId: number;
  boekestynPlantId: string | null;
  boekestynCustomerAbbreviation: string | null;
  upgradeLabourHours: number | null;
  quantityPerFinishedItem: number | null;
  isUpgrade: boolean;
  ignoreOverrideQuantity: boolean;
  products: settings.ProductDefaultBoekestynProduct[];
  overrides: settings.ProductDefaultBoekestynProductOverride[];
  updateExisting: boolean;
}

export interface PriceDeviationWarningResponse {
  priceDeviationWarnings: settings.PriceDeviationWarning[];
}

export interface DefaultVendorOverrideResponse {
  overrides: settings.DefaultVendorOverride[];
}

export interface UpdateShipToDefaultArgs {
  shipToCode: string;
  defaultFreightPerCase: number | null;
}

export interface GetFreightRatesResponse {
  freightRates: settings.PriceLevelFreightRate[];
  priceLevels: spire.PriceLevel[];
}

export interface UpdateFreightRateArgs {
  priceLevel: string;
  defaultFreightPerCase: number | null;
}

export const settingsService = new SettingsService();
export const {
  usePriceDeviationWarningsQuery,
  useDefaultVendorOverridesQuery,
  useCreateDefaultVendorOverrideMutation,
  useUpdateDefaultVendorOverrideMutation,
  useDeleteDefaultVendorOverrideMutation,
  useUpdateShipToDefaultMutation,
  useGetFreightRatesQuery,
  useUpdateFreightRateMutation,
} = settingsApi;
