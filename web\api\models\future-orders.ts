export const SpecialLabels = 'special';

export interface FutureOrderListItem {
  id: number;
  requiredDate: string | null;
  seasonDate: string | null;
  season: string | null;
  customer: string;
  shipTo: string | null;
  salesperson: string | null;
  truck: string | null;
  customerPurchaseOrderNumber: string | null;
  requiresLabels: boolean;
  phytoRequired: boolean;
  phytoOrdered: boolean;
  caseCount: number;
  dollarValue: number;
  isParentOrder: boolean;
  parentOrderId: number | null;
  spireSalesOrderNumber: string | null;
  created: string;
  createdBy: string;
  modified: string;
  modifiedBy: string;
  sentToSpire: string | null;
  sentToSpireBy: string | null;

  prebooks: FutureOrderListItemPrebook[];
}

export interface FutureOrderSummaryItem {
  id: number;
  customer: string | null;
  shipTo: string | null;
  vendor: string | null;
  salesperson: string | null;
  season: string | null;
  date: string | null;
  seasonDate: string | null;
  spirePartNumber: string;
  description: string;
  customerItemCode: string | null;
  orderQuantity: number;
  created: string;
  createdBy: string;
  modified: string;
  modifiedBy: string;
  sentToSpire: string | null;
  sentToSpireBy: string | null;
  futureOrderId: number;
  prebookIdList: string;
  prebookEmailSent: boolean;
}

export interface UpgradeItem {
  id: number;
  date: string | null;
  season: string | null;
  spireInventoryId: number;
  spirePartNumber: string;
  description: string;
  comments: string | null;
  orderQuantity: number;
  customerId: number | null;
  customer: string | null;
  shipTo: string | null;
  boxCode: string | null;
  upc: string | null;
  dateCode: string | null;
  retail: string | null;
  potCover: string | null;
  weightsAndMeasures: boolean;
  containerPickDescription: string | null;
  upgradeComments: string | null;
  productComingFrom: string | null;
  upcComment: string | null;
  upcApprovalRequired: boolean;
  origins: string | null;
  costs: string | null;
  labourHours: number;
  growerItemNotes: string | null;
  itemGrowerItemNotes: string | null;
  upgradeConfirmed: string | null;
  upgradeConfirmedBy: string | null;
  priority: boolean;
  upcPrinted: string | null;
  upcPrintedPrev: string | null;
  isApproximate: boolean;
  futureOrderId: number | null;
  prebookId: number;
}

export interface UpgradeItemAttachment {
  id: number;
  prebookItemId: number;
  filename: string;
}

export interface FutureOrderListItemPrebook {
  id: number;
  futureOrderId: number;
  vendor: string;
  caseCount: number;
  created: string;
  createdBy: string;
  modified: string;
  modifiedBy: string;
  sent: string | null;
  sentBy: string | null;
  confirmed: string | null;
  confirmedBy: string | null;
}

export interface FutureOrderCreate {
  requiredDate: string | null;
  arrivalDate: string | null;
  seasonName: string | null;
  customerId: number | null;
  customerName: string | null;
  shipToId: number | null;
  shipToName: string | null;
  salespersonId: number | null;
  salespersonName: string | null;
  shipViaId: number | null;
  shipViaName: string | null;
  boxCode: string | null;
  requiresLabels: boolean;
  customerPurchaseOrderNumber: string | null;
  spireNotes: string | null;
  growerItemNotes: string | null;
  freightPerCase: number | null;
  freightPerLoad: number | null;
  freightIsActual: boolean;

  items: FutureOrderCreateItem[];
  comments: FutureOrderCreateComment[];
}

export interface FutureOrderCreateItem {
  id: number;
  sortOrder: number;
  vendorId: number | null;
  vendorName: string | null;
  spireInventoryId: number | null;
  spirePartNumber: string | null;
  description: string | null;
  orderQuantity: number;
  isApproximate: boolean;
  hasPotCover: boolean;
  potCover: string | null;
  dateCode: string | null;
  upc: string | null;
  weightsAndMeasures: boolean;
  retail: string | null;
  comments: string | null;
  createPrebook: boolean;
  isBlanket: boolean;
  blanketItemId: number | null;
  unitPrice: number | null;
  useAvailabilityPricing: boolean;
  customerItemCode: string | null;
  upgradeSheet: boolean;
  phytoRequired: boolean;
  boekestynPlantId: string | null;
  boekestynCustomerAbbreviation: string | null;
  upgradeLabourHours: number | null;
  quantityPerFinishedItem: number | null;
  specialPrice: number | null;
  growerItemNotes: string | null;

  boekestynProducts: FutureOrderBoekestynProduct[];
}

export interface FutureOrderBoekestynProduct {
  boekestynPlantId: string;
  boekestynCustomerAbbreviation: string | null;
  quantityPerFinishedItem: number;
}

export interface FutureOrderCreateComment {
  id: number;
  comments: string | null;
  isStandardComment: boolean;
}

export interface FutureOrderDetail {
  id: number;
  requiredDate: string | null;
  arrivalDate: string | null;
  seasonName: string | null;
  customerId: number | null;
  customerName: string | null;
  shipToId: number | null;
  shipToName: string | null;
  salespersonId: number | null;
  salespersonName: string | null;
  shipViaId: number | null;
  shipViaName: string | null;
  boxCode: string | null;
  requiresLabels: boolean;
  customerPurchaseOrderNumber: string | null;
  spireNotes: string | null;
  growerItemNotes: string | null;
  freightPerCase: number | null;
  freightPerLoad: number | null;
  freightIsActual: boolean;
  spireSalesOrderId: number | null;
  isParentOrder: boolean;
  parentOrderId: number | null;
  spireSalesOrderNumber: string | null;
  created: string;
  createdBy: string;
  modified: string;
  modifiedBy: string;
  sentToSpire: string | null;
  sentToSpireBy: string | null;

  comments: FutureOrderDetailComment[];
  items: FutureOrderDetailItem[];
}

export interface FutureOrderDetailItem {
  id: number;
  sortOrder: number;
  vendorId: number | null;
  vendorName: string | null;
  spireInventoryId: number | null;
  spirePartNumber: string | null;
  description: string | null;
  orderQuantity: number;
  isApproximate: boolean;
  hasPotCover: boolean;
  potCover: string | null;
  dateCode: string | null;
  upc: string | null;
  weightsAndMeasures: boolean;
  retail: string | null;
  comments: string | null;
  blanketItemId: number | null;
  unitPrice: number | null;
  useAvailabilityPricing: boolean;
  customerItemCode: string | null;
  upgradeSheet: boolean;
  phytoRequired: boolean;
  phytoOrdered: boolean;
  boekestynPlantId: string | null;
  boekestynCustomerAbbreviation: string | null;
  upgradeLabourHours: number;
  quantityPerFinishedItem: number | null;
  specialPrice: number | null;
  growerItemNotes: string | null;

  boekestynProducts: FutureOrderBoekestynProduct[];
}

export interface FutureOrderDetailComment {
  id: number;
  comments: string | null;
  isStandardComment: boolean;
  created: string | null;
  createdBy: string | null;
}

export interface FutureOrderUpdate {
  id: number;
  requiredDate: string | null;
  arrivalDate: string | null;
  seasonName: string | null;
  customerId: number | null;
  customerName: string | null;
  shipToId: number | null;
  shipToName: string | null;
  salespersonId: number | null;
  salespersonName: string | null;
  shipViaId: number | null;
  shipViaName: string | null;
  boxCode: string | null;
  requiresLabels: boolean;
  customerPurchaseOrderNumber: string | null;
  freightPerCase: number | null;
  freightPerLoad: number | null;
  freightIsActual: boolean;
  spireNotes: string | null;
  growerItemNotes: string | null;

  items: FutureOrderUpdateItem[];
  comments: FutureOrderUpdateComment[];
}

export interface FutureOrderUpdateComment {
  id: number;
  comments: string | null;
}

export interface FutureOrderUpdateItem extends FutureOrderDetailItem {
  createPrebook: boolean;
  isBlanket: boolean;
}

export interface ProductCustomerDefault {
  id: number;
  spireInventoryId: number;
  customerId: number;
  customerItemCode: string | null;
}

export interface DeletedFutureOrder {
  id: number;
  requiredDate: string | null;
  customerName: string | null;
  shipToName: string | null;
  customerPurchaseOrderNumber: string | null;
  salespersonName: string | null;
  spireSalesOrderNumber: string | null;
  deleted: string;
  deletedBy: string;
}

export interface FutureOrderSplit {
  existingFutureOrderId: number | null;
  requiredDate: string | null;
  arrivalDate: string | null;
  seasonName: string | null;
  customerId: number | null;
  customerName: string | null;
  shipToId: number | null;
  shipToName: string | null;
  salespersonId: number | null;
  salespersonName: string | null;
  shipViaId: number | null;
  shipViaName: string | null;
  boxCode: string | null;
  requiresLabels: boolean;
  customerPurchaseOrderNumber: string | null;
  freightPerCase: number | null;
  freightPerLoad: number | null;
  freightIsActual: boolean;
  spireNotes: string | null;
  growerItemNotes: string | null;

  items: FutureOrderSplitItem[];
  comments: FutureOrderSplitComment[];
}

export interface FutureOrderSplitItem {
  originalId: number;
  orderQuantity: number;
}

export interface FutureOrderSplitComment {
  comments: string | null;
  isStandardComment: boolean;
}

export interface FutureOrderChild {
  id: number;
  requiredDate: string | null;
  customerName: string | null;
}
