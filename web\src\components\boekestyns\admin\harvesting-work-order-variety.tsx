import { useMemo, useEffect } from 'react';
import { formatNumber } from '@/utils/format';
import * as HeadlessUI from '@headlessui/react';
import {
  setVarietySelection,
  setVarietyExpectedPercentage,
  WorkOrderData,
  selectSchedules,
  ScheduleData,
  VarietyData,
  selectSelectedOrder,
} from './harvesting-work-orders-slice';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { handleFocus } from '@/utils/focus';
import { Icon } from '@/components/icon';

export type HarvestingWorkOrderVarietyRowProps = {
  schedule: ScheduleData;
  variety: VarietyData;
  workOrder: WorkOrderData;
};

export function HarvestingWorkOrderVarietyRow({
  schedule,
  variety,
  workOrder,
}: HarvestingWorkOrderVarietyRowProps) {
  const dispatch = useAppDispatch(),
    schedules = useAppSelector(selectSchedules),
    order = useAppSelector(selectSelectedOrder),
    finalRound = workOrder.finalRound,
    varietyData = useMemo(
      () => order?.varieties.find((v) => v.name === variety.name),
      [order?.varieties, variety.name]
    ),
    varietyScheduledPots = useMemo(
      () =>
        schedules.reduce((total, s) => {
          if (s.date >= schedule.date) return total;

          const scheduledVariety = s.workOrder.varieties.find(
              (v) => v.name === variety.name
            ),
            percentage =
              (scheduledVariety?.expectedHarvestPercentage ?? 100) / 100,
            pots = scheduledVariety?.pots ?? 0,
            scheduled = Math.round((pots - total) * percentage);

          return total + scheduled;
        }, 0),
      [schedules, schedule.date, variety.name]
    ),
    varietyAvailablePots = useMemo(() => {
      if (!varietyData) return 0;

      return varietyData.pots - varietyScheduledPots;
    }, [varietyData, varietyScheduledPots]),
    varietyToSchedule = useMemo(() => {
      if (finalRound) {
        return varietyAvailablePots;
      }

      const scheduledForThisRound = Math.round(
        varietyAvailablePots *
          ((variety?.expectedHarvestPercentage ?? 100) / 100)
      );

      return Math.min(scheduledForThisRound, varietyAvailablePots);
    }, [finalRound, varietyAvailablePots, variety?.expectedHarvestPercentage]),
    varietyOverscheduled = useMemo(() => {
      // check if there are scheduled dates with 100% expected harvest percentage followed by a non-zero value
      let fullDay = false;

      for (let i = 0; i < schedules.length; i++) {
        const s = schedules[i],
          scheduledVariety = s.workOrder.varieties.find(
            (v) => v.name === variety.name
          ),
          percentage = scheduledVariety?.expectedHarvestPercentage ?? 100;

        if (percentage > 0 && fullDay) {
          return true;
        }

        if (percentage === 100) {
          fullDay = true;
        }
      }

      return false;

    }, [varietyScheduledPots, varietyData?.pots]);

  const handleSelectVarietyChanged = (selected: boolean) => {
    dispatch(
      setVarietySelection({
        workOrderId: workOrder.id,
        varietyName: variety.name,
        selected,
      })
    );
  };

  const handleExpectedHarvestPercentageChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.valueAsNumber,
      percentage = isNaN(value) ? 0 : value;

    dispatch(
      setVarietyExpectedPercentage({
        workOrderId: workOrder.id,
        varietyName: variety.name,
        percentage,
      })
    );
  };

  return (
    <tr key={variety.name}>
      <td className="whitespace-nowrap p-2 text-left">{variety.name}</td>
      <td className="w-1 p-2 text-right">
        {formatNumber(variety.pots, '0,0')}
      </td>
      <td className="w-1 p-2 text-right">
        {formatNumber(varietyScheduledPots, '0,0')}
      </td>
      <td className="w-1 p-2 text-right">
        {formatNumber(varietyAvailablePots, '0,0')}
      </td>
      <td className="w-1 p-2 text-right">
        <input
          type="number"
          value={finalRound ? 100 : (variety?.expectedHarvestPercentage ?? 100)}
          onChange={handleExpectedHarvestPercentageChange}
          onFocus={handleFocus}
          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          min="0"
          max="100"
          step="1"
          disabled={finalRound}
        />
      </td>
      <td className="w-1 p-2 text-right">
        {formatNumber(varietyToSchedule, '0,0')}
      </td>
      <td className="text-center">
        <HeadlessUI.Switch
          onChange={handleSelectVarietyChanged}
          checked={!!variety?.selected}
          className={classNames(
            variety?.selected ? 'bg-blue-400' : 'bg-gray-200',
            'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
          )}
        >
          <span
            aria-hidden="true"
            className={classNames(
              variety?.selected ? 'translate-x-5' : 'translate-x-0',
              'pointer-events-none inline-block size-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
            )}
          />
        </HeadlessUI.Switch>
        {varietyOverscheduled && (
          <
          <div className="text-yellow-500">
            <Icon icon="exclamation-triangle" />
          </div>
        )}
      </td>
    </tr>
  );
}
