import { DateTime } from 'luxon';
import { CreateItem } from '@/components/future-orders/future-order-create-slice';
import { DetailItem } from '@/components/future-orders/future-order-detail-slice';
import { NewPrebookDetailItem, PrebookDetailItem } from './prebooks';
import { contains, equals } from '@/utils/equals';

export const BoekestynVendorId = 143;
export const BoekestynVendorName = 'BOEKESTYN GREENHOUSES';
export const WeeklyCustomerName = 'Weekly';
export const WeeklyCustomerAbbreviation = 'Wk';
export const UpgradesVendorId = 378;

export const OrderType = 'order';
export const PlantType = 'plant';

export const SpacingTypeTight = 'Tight';
export const SpacingTypePartial = 'Partial';
export const SpacingTypeFull = 'Full';

export type SpacingTypes =
  | typeof SpacingTypeTight
  | typeof SpacingTypePartial
  | typeof SpacingTypeFull;

export interface WeeklyProductionOrder {
  customer: string;
  weekNumber: number;
  year: number;
  plantId: string;
  description: string;
  salesWeeks?: SalesWeek[];
}

export interface SalesOrder {
  id: string;
  customer: string;
  productionCustomer: string;
  week: number;
  year: number;
  plant: Plant;
  quantity: number;
  comment: string | null;
}

export interface SalesWeek {
  id: string;
  week: string;
  cases: number;
}

export interface Plant {
  _id: string;
  name: string;
  abbreviation: string;
  potsPerCase: number;
}

export interface Customer {
  name: string;
  abbreviation: string;
}

export interface ItemListItem {
  id: number;
  prebookId: number;
  futureOrderId: number | null;
  requiredDate: string | null;
  season: string | null;
  customer: string | null;
  shipTo: string | null;
  comments: string | null;
  growerItemNotes: string | null;
  itemGrowerItemNotes: string | null;
  spirePartNumber: string;
  description: string;
  boxCode: string | null;
  potCover: string | null;
  upc: string | null;
  dateCode: string | null;
  retail: string | null;
  weightsAndMeasures: boolean;
  itemComments: string | null;
  priority: boolean;
  upcPrinted: string | null;
  upcPrintedPrev: string | null;
  isApproximate: boolean;
  caseCount: number;
  packQuantity: number | null;
  created: string;
  createdBy: string;
  modified: string;
  modifiedBy: string;
}

export interface ItemListRequirementItem {
  id: number;
  prebookId: number;
  product: string;
  requiredDate: string;
  boxCode: string;
  potCover: string;
  comments: string | null;
  orderQuantity: number;
}

export interface BoekestynProductionOrder {
  id: string;
  revision: string;
  type: string;
  cases: number;
  flowerDate: string;
  customer: ProductionOrderCustomer;
  plant: Plant;
}

interface ProductionOrderCustomer {
  abbreviation: string;
  name: string;
}

export interface StickingLine {
  id: number;
  name: string;
}

export interface StickingOrder {
  _id: string;
  _rev?: string | null;
  type: string;
  orderNumber: string;
  stickDate: string;
  hasPartialSpace: boolean;
  hasSpacing: boolean;
  fullSpaceDate?: string | null;
  hasLightsOut: boolean;
  lightsOutDate?: string | null;
  hasPinching: boolean;
  flowerDate: string;
  cuttings: number;
  pots: number;
  cases: number;
  pinchDate?: string | null;
  tableCountTight: number;
  tableCountSpaced: number;
  notes?: string | null;

  stickingHours: number;
  stickingScheduled: boolean;

  customer: StickingOrderCustomer;
  plant: StickingOrderPlant;
  varieties: StickingOrderVariety[];
  stickZone: StickingOrderZone;
  fullSpaceZone?: StickingOrderZone;
  lightsOutZone?: StickingOrderZone;
}

interface StickingOrderCustomer {
  abbreviation: string;
  name: string;
}

export interface StickingOrderPlant {
  _id: string;
  name: string;
  abbreviation: string;
  crop: string;
  size: string;
  cuttingsPerPot: number;
  cuttingsPerTableTight: number;
  cuttingsPerTableSpaced: number;
  cuttingsPerTablePartiallySpaced: number;
  potsPerCase: number;
  stickingCuttingsPerHour: number;
  spacingPotsPerHour: number;
  packingCasesPerHour: number;
  colour: string;
  hasPinching: boolean;
  daysToPinch: number;
  defaultStickingCrewSize: number | null;
}

interface StickingOrderVariety {
  name: string;
  cuttings: number;
  pots: number;
  cases: number;
  comment?: string | null;
}

interface StickingOrderZone {
  _id: string;
  name: string;
  tables: number;
}

export interface StickingSchedule {
  id: number;
  date: string;
  lineId: number;

  workOrders: StickingWorkOrder[];
}

export interface StickingWorkOrder {
  id: number;
  scheduleId: number;
  sortOrder: number;
  orderId: string;
  orderNumber: string;
  plantSize: string;
  plantCrop: string;
  customer: string;
  cuttings: number;
  pots: number;
  cases: number;
  zone: string;
  estimatedHours: number;
  crewSize: number;
  orderComments: string | null;
  stickingComments: string | null;
  flowerDate: string;
  color: string;

  varieties: StickingWorkOrderVariety[];
}

export interface StickingWorkOrderItem extends StickingWorkOrder {
  lineName: string;
  scheduleDate: string | null;

  labour: StickingWorkOrderLabour[];
}

export interface StickingWorkOrderVariety {
  id: number;
  workOrderId: number;
  name: string;
  pots: number;
  cuttings: number;
  cases: number;
  comment?: string | null;
}

export interface StickingWorkOrderLabour {
  id: number;
  workOrderId: number;
  crewSize: number;
  startTime: string;
  endTime: string | null;
  comments: string | null;
  finalLabour: boolean;
}

export interface SpacingLine {
  id: number;
  name: string;
}

export interface SpacingOrder {
  _id: string;
  _rev?: string | null;
  type: string;
  orderNumber: string;
  stickDate: string;
  hasPartialSpace: boolean;
  hasSpacing: boolean;
  fullSpaceDate?: string | null;
  hasLightsOut: boolean;
  lightsOutDate?: string | null;
  hasPinching: boolean;
  flowerDate: string;
  cuttings: number;
  pots: number;
  cases: number;
  pinchDate?: string | null;
  tableCountTight: number;
  tableCountSpaced: number;
  notes?: string | null;

  spacingHours: number;
  potsPartiallySpaced: number;
  potsFullySpaced: number;

  customer: SpacingOrderCustomer;
  plant: SpacingOrderPlant;
  varieties: SpacingOrderVariety[];
  stickZone: SpacingOrderZone;
  fullSpaceZone?: SpacingOrderZone;
  lightsOutZone?: SpacingOrderZone;
}

interface SpacingOrderCustomer {
  abbreviation: string;
  name: string;
}

interface SpacingOrderPlant {
  _id: string;
  name: string;
  abbreviation: string;
  crop: string;
  size: string;
  cuttingsPerPot: number;
  cuttingsPerTableTight: number;
  cuttingsPerTableSpaced: number;
  cuttingsPerTablePartiallySpaced: number;
  potsPerCase: number;
  stickingCuttingsPerHour: number;
  spacingPotsPerHour: number;
  packingCasesPerHour: number;
  colour: string;
  hasPinching: boolean;
  daysToPinch: number;
  defaultStickingCrewSize: number | null;
}

interface SpacingOrderVariety {
  name: string;
  cuttings: number;
  pots: number;
  cases: number;
  comment?: string | null;
}

interface SpacingOrderZone {
  _id: string;
  name: string;
  tables: number;
}

export interface SpacingSchedule {
  id: number;
  date: string;
  lineId: number;

  workOrders: SpacingWorkOrder[];
}

export interface SpacingWorkOrder {
  id: number;
  scheduleId: number;
  sortOrder: number;
  orderId: string;
  orderNumber: string;
  plantSize: string;
  plantCrop: string;
  plantHasPinching: boolean;
  spacingPotsPerHour: number;
  cuttingsPerPot: number;
  cuttingsPerTableTight: number;
  cuttingsPerTableSpaced: number;
  cuttingsPerTablePartiallySpaced: number;
  customer: string;
  orderPots: number;
  orderCases: number;
  zone: string;
  estimatedHours: number;
  potsToSpace: number;
  crewSize: number;
  fromSpaceType: SpacingTypes;
  toSpaceType: SpacingTypes;
  requiresPinching: boolean;
  orderComments: string | null;
  spacingComments: string | null;
  robotProgram: string | null;

  varieties: SpacingWorkOrderVariety[];
}

export interface SpacingWorkOrderItem extends SpacingWorkOrder {
  lineName: string;
  scheduleDate: string | null;

  labour: SpacingWorkOrderLabour[];
}

export interface SpacingWorkOrderVariety {
  id: number;
  workOrderId: number;
  name: string;
  pots: number;
  cuttings: number;
  cases: number;
  comment?: string | null;
}

export interface SpacingWorkOrderLabour {
  id: number;
  workOrderId: number;
  crewSize: number;
  startTime: string;
  endTime: string | null;
  comments: string | null;
  finalLabour: boolean;
}

export interface HarvestingSchedule {
  id: number;
  date: string;
  lineId: number;

  workOrders: HarvestingWorkOrder[];
}

export interface HarvestingLine {
  id: number;
  name: string;
}

export interface HarvestingAdminOrderItem {
  stickingWorkOrderId: number;
  orderId: string;
  orderNumber: string;
  plantSize: string;
  plantCrop: string;
  customer: string;
  pots: number;
  colour: string | null;
  flowerDate: string;

  rounds: HarvestingOrderRound[];
  varieties: HarvestingOrderVariety[];
}

export interface HarvestingOrder {
  _id: string;
  _rev?: string | null;
  type: string;
  orderNumber: string;
  flowerDate: string;
  cuttings: number;
  pots: number;
  cases: number;
  tableCountSpaced: number;
  notes?: string | null;
  customer: HarvestingOrderCustomer;
  plant: HarvestingOrderPlant;
  varieties: HarvestingOrderVariety[];
  rounds: HarvestingOrderRound[];
  stickingWorkOrder: number | null;
}

interface HarvestingOrderCustomer {
  abbreviation: string;
  name: string;
}

export interface HarvestingOrderPlant {
  _id: string;
  name: string;
  abbreviation: string;
  crop: string;
  size: string;
  cuttingsPerPot: number;
  potsPerCase: number;
  colour: string;
  maximumHarvestRounds: number;
  daysBetweenHarvestRounds: number;
}

export interface HarvestingOrderRound {
  workOrderId: number;
  orderId: string;
  roundNumber: number;
  pots: number;
  cases: number;
  date: string;
  varieties: HarvestingOrderRoundVariety[];
}

export interface HarvestingOrderRoundVariety {
  workOrderId: number;
  roundNumber: number;
  name: string;
  harvested: number;
  thrownOut: number;
}

export interface HarvestingOrderVariety {
  name: string;
  pots: number;
  cases: number;
  comment: string | null;
}

export interface HarvestingWorkOrder {
  stickingWorkOrderId: number;
  id: number;
  scheduleId: number;
  sortOrder: number;
  orderId: string;
  orderNumber: string;
  plantSize: string;
  plantCrop: string;
  customer: string;
  pots: number;
  cases: number;
  zone: string;
  estimatedHours: number;
  crewSize: number;
  orderComments: string | null;
  harvestingComments: string | null;
  defaultExpectedHarvestPercentage: number;
  finalRound: boolean;
  varieties: HarvestingWorkOrderVariety[];
}

export interface HarvestingWorkOrderItem extends HarvestingWorkOrder {
  lineName: string;
  scheduleDate: string | null;
  previousDaysLabourMinutes: number | null;

  labour: HarvestingWorkOrderLabour[];
}

export interface HarvestingWorkOrderVariety {
  id: number;
  workOrderId: number;
  name: string;
  pots: number;
  expectedHarvestPercentage: number;
  comment: string | null;
}

export interface HarvestingWorkOrderLabour {
  id: number;
  workOrderId: number;
  crewSize: number;
  startTime: string;
  endTime: string | null;
  comments: string | null;
  finalLabour: boolean;
  harvestComplete: boolean;
}

export interface HarvestingWorkOrderLabourVarietyItem {
  id: number;
  varietyName: string;
  harvested: number;
  thrownOut: number;
  comments: string | null;
}

export interface HarvestingWorkOrderVarietyHarvest {
  id: number;
  workOrderLabourId: number;
  workOrderVarietyId: number;
  harvested: number;
  thrownOut: number;
  comment?: string | null;
}

export interface BoekestynPrebookItem {
  id: number;
  prebookId: number;
  futureOrderId: number | null;
  spirePartNumber: string;
  description: string;
  customerName: string;
  shipToName: string;
  boxCode: string;
  week: number;
  year: number;
  orderQuantity: number;
  boekestynPlantId: string | null;
  boekestynCustomerAbbreviation: string;
  quantityPerFinishedItem: number | null;
  multipleProducts: boolean;
  packQuantity: number;
  ignoreOverrideQuantity: boolean;
  couchCustomer: string;
}

export interface BoekestynPrebookItemProduct {
  id: number;
  boekestynPlantId: string;
  boekestynCustomerAbbreviation: string | null;
  quantityPerFinishedItem: number;
}

export interface BoekestynPrebookItemProductOverride {
  id: number;
  startWeek: number;
  endWeek: number;
  boekestynPlantId: string;
  boekestynCustomerAbbreviation: string | null;
  quantityPerFinishedItem: number;
}

export interface BoekestynTask {
  id: number;
  purchaseOrderNumber: string | null;
  requiredDate: string | null;
  items: BoekestynTaskItem[];
  reportUrl: string | null;
}

export interface BoekestynTaskItem {
  id: number;
  poNumber: string;
  orderQty: number;
  description: string;
  boxCode: string;
  comment: string;
  packQuantity: number | null;
  priority: boolean;
  prepStart: string | null;
  prepComplete: string | null;
  packStart: string | null;
  packComplete: string | null;
  upcComplete: string | null;
  goodSkids: boolean;
}

export function ordersWithAllPlants(
  requiredDate: string | null,
  item: CreateItem | DetailItem | PrebookDetailItem | NewPrebookDetailItem,
  boekestynOrders: WeeklyProductionOrder[],
  plants: Plant[]
) {
  const lux = requiredDate ? DateTime.fromISO(requiredDate) : DateTime.now(),
    weekNumber = lux.get('weekNumber'),
    year = lux.get('weekYear'),
    orders = boekestynOrders
      .filter(
        (o) =>
          (!Array.isArray(o.salesWeeks) &&
            year === o.year &&
            weekNumber === o.weekNumber) ||
          (Array.isArray(o.salesWeeks) &&
            o.salesWeeks.some((sw) => sw.week === `${o.weekNumber}/${o.year}`))
      )
      .concat(
        plants.map((p) => ({
          id: '',
          customer: WeeklyCustomerAbbreviation,
          weekNumber,
          year,
          plantId: p._id,
          description: p.name,
          orderQuantity: 0,
          salesOrders: [],
        }))
      )
      .filter(
        (o) => !item.boekestynPlantId || item.boekestynPlantId === o.plantId
      )
      .reduce((memo, o) => {
        if (
          !memo.some(
            (m) => m.plantId === o.plantId && m.customer === o.customer
          )
        ) {
          memo.push(o);
        }
        return memo;
      }, [] as WeeklyProductionOrder[]);

  if (
    item.boekestynPlantId &&
    !orders.some(
      (o) =>
        o.plantId === item.boekestynPlantId &&
        o.customer === item.boekestynCustomerAbbreviation
    )
  ) {
    var plant = plants.find((p) => p._id === item.boekestynPlantId),
      description =
        plant?.name || item.description || item.spirePartNumber || '';
    orders.push({
      customer: item.boekestynCustomerAbbreviation || '',
      weekNumber,
      year,
      plantId: item.boekestynPlantId,
      description,
    });
  }

  return orders;
}

export const spacingRobotPrograms = [
  '6" Lily Tight',
  '6" Mum',
  '4.5" Mums',
  '4.5" Gerb Reg Beg Half',
  '6" Gerb & Mum Half',
  '6" Half Space',
  '4.5" Begonia Sweetie',
  '4.5" Cyclamen',
  '6" Tight',
  '4.5" Frosty Fern',
  '4.5" Cyclamen Re-Space',
  '4.5" Hydrangea',
  '4.5" Tight Alt',
  '6" Lily',
  '4.5" Lisianthus',
  '4.5" Kalanchoe',
  '6" Lisianthus',
  '6" Tight Alt',
  '6" Cyclamen',
  '6" Oriental',
];

export function findSpacingRobotProgram(
  { size, crop }: SpacingOrderPlant,
  spacing: SpacingTypes
) {
  if (contains(size, '4.5"')) {
    if (equals(spacing, SpacingTypeTight)) {
      return '4.5" Tight Alt';
    }

    if (equals(spacing, SpacingTypePartial) && equals(crop, 'Begonia')) {
      return '4.5" Gerb Reg Beg Half';
    }

    if (equals(crop, 'Mum')) {
      return '4.5" Mums';
    }
    if (equals(crop, 'Gerbera')) {
      return '4.5" Gerb Reg Beg Half';
    }
    if (equals(crop, 'Begonia')) {
      return '4.5" Begonia Sweetie';
    }
    if (equals(crop, 'Cyclamen')) {
      return '4.5" Cyclamen';
    }
    if (equals(crop, 'Hydrangea')) {
      return '4.5" Hydrangea';
    }
    if (equals(crop, 'Lisianthus')) {
      return '4.5" Lisianthus';
    }
    if (equals(crop, 'Kalanchoe')) {
      return '4.5" Kalanchoe';
    }
  } else if (contains(size, '6"')) {
    if (equals(spacing, SpacingTypeTight) && equals(crop, 'Lily')) {
      return '6" Lily Tight';
    }
    if (equals(spacing, SpacingTypeTight)) {
      return '6" Tight Alt';
    }

    if (equals(spacing, SpacingTypePartial) && equals(crop, 'Mum')) {
      return '6" Gerb & Mum Half';
    }
    if (equals(spacing, SpacingTypePartial)) {
      return '6" Half Space';
    }
    if (equals(spacing, SpacingTypeTight)) {
      return '6" Tight';
    }

    if (equals(crop, 'Mum')) {
      return '6" Mum';
    }
    if (equals(crop, 'Gerbera')) {
      return '6" Gerb & Mum Half';
    }
    if (equals(crop, 'Mum')) {
      return '6" Mum';
    }
    if (equals(crop, 'Lily')) {
      return '6" Lily';
    }
    if (equals(crop, 'Lisianthus')) {
      return '6" Lisianthus';
    }
    if (equals(crop, 'Cyclamen')) {
      return '6" Cyclamen';
    }
    if (equals(crop, 'Oriental')) {
      return '6" Oriental';
    }
  }
  return '';
}
