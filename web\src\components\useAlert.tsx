import { useState, useRef } from 'react';
import { Alert, AlertProps } from './alert';

interface UseAlertResult {
  show: (
    props: Omit<AlertProps, 'open' | 'cancel' | 'confirm'>
  ) => Promise<boolean>;
  alertJSX: JSX.Element | null;
}

export function useAlert(): UseAlertResult {
  const [alertProps, setAlertProps] = useState<Omit<AlertProps, 'open'>>();
  const promiseRef = useRef<{
    resolve: (value: boolean) => void;
    reject: (reason?: any) => void;
  }>();

  function show(
    props: Omit<AlertProps, 'open' | 'cancel' | 'confirm'>
  ): Promise<boolean> {
    return new Promise((resolve, reject) => {
      setAlertProps({
        ...props,
        cancel: () => {
          setAlertProps(undefined);
          resolve(false);
        },
        confirm: () => {
          setAlertProps(undefined);
          resolve(true);
        },
      });
      promiseRef.current = { resolve, reject };
    });
  }

  const alertJSX = alertProps ? (
    <Alert
      {...alertProps}
      open={true}
      cancel={alertProps.cancel}
      confirm={alertProps.confirm}
    />
  ) : null;

  return { show, alertJSX };
}
