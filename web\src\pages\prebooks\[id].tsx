import React, { Fragment, useEffect, useRef, useState } from 'react';
import { GlobalHotKeys, configure as configureHotkeys } from 'react-hotkeys';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import * as HeadlessUI from '@headlessui/react';
import { Combobox } from '@/components/combo-box';
import { Error } from '@/components/error';
import { Alert } from '@/components/alert';
import { Icon } from '@/components/icon';
import { CreateSeason } from '@/components/create-season';
import { Loading } from '@/components/loading';
import { ShipToCombobox } from '@/components/ship-to-combo-box';
import { Email } from '@/components/prebooks/email';
import { EmailReview } from '@/components/prebooks/email-review';
import { Item } from '@/components/prebooks/item';
import { Inventory } from '@/components/prebooks/inventory';
import { apiUrl } from 'api/api-base';
import { useSeasonsQuery, prebooksApi } from 'api/prebooks-service';
import {
  useCustomersQuery,
  useVendorsQuery,
  useSalespeopleQuery,
} from 'api/spire-service';
import * as prebooks from 'api/models/prebooks';
import * as spire from 'api/models/spire';
import { usePermissions } from '@/services/auth';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import { noPotCover } from '@/components/pot-covers';
import { itemIsOverbooked } from '@/components/prebooks/item-functions';
import {
  selectError,
  selectSeasonName,
  selectBoxCode,
  selectComments,
  selectGrowerItemNotes,
  selectCustomer,
  selectItems,
  selectName,
  selectRequiredDate,
  selectSalesperson,
  selectShipTo,
  selectVendor,
  setCustomer,
  setCustomerDetail,
  setName,
  setSalesperson,
  setVendor,
  setShipTo,
  setRequiredDate,
  setSeasonName,
  setBoxCode,
  setComments,
  setGrowerItemNotes,
  addItem,
  getPrebookDetail,
  selectIsLoading,
  selectCustomerDetail,
  selectShipTos,
  getCustomerDetail,
  updatePrebook,
  deletePrebook,
  selectPrebookDetail,
  setError,
  clearError,
  clearState,
  selectEmails,
  InventoryItemWithDefaults,
  sendToSpire,
  setIsBlanket,
  selectIsBlanket,
  setBlanketIsClosed,
  selectBlanketIsClosed,
  setBlanketStartDate,
  selectBlanketStartDate,
  selectBlanketItems,
  setGrowerConfirmed,
  getBlanketItems,
  setItemBoekestynProducts,
  selectProductDefaults,
} from '@/components/prebooks/prebook-detail-slice';
import { BoekestynProduct } from '@/components/prebooks/boekestyn-product-slice';
import { BoekestynProducts } from '@/components/prebooks/boekestyn-products';
import { classNames } from '@/utils/class-names';
import { dateIsInPast, startsWith } from '@/utils/equals';
import { formatDate, formatNumber, parseQuantity } from '@/utils/format';
import { createProblemDetails } from '@/utils/problem-details';

export default function New() {
  const dispatch = useAppDispatch(),
    router = useRouter(),
    { can } = usePermissions(),
    { id, action } = router.query,
    { data: vendors } = useVendorsQuery(),
    { data: customers } = useCustomersQuery(),
    { data: salespeople } = useSalespeopleQuery(),
    { data: seasons } = useSeasonsQuery(),
    error = useAppSelector(selectError),
    name = useAppSelector(selectName),
    vendor = useAppSelector(selectVendor),
    shipTo = useAppSelector(selectShipTo),
    customer = useAppSelector(selectCustomer),
    salesperson = useAppSelector(selectSalesperson),
    requiredDate = useAppSelector(selectRequiredDate),
    isBlanket = useAppSelector(selectIsBlanket),
    blanketStartDate = useAppSelector(selectBlanketStartDate),
    blanketIsClosed = useAppSelector(selectBlanketIsClosed),
    seasonName = useAppSelector(selectSeasonName),
    boxCode = useAppSelector(selectBoxCode),
    comments = useAppSelector(selectComments),
    growerItemNotes = useAppSelector(selectGrowerItemNotes),
    items = useAppSelector(selectItems),
    blanketItems = useAppSelector(selectBlanketItems),
    isLoading = useAppSelector(selectIsLoading),
    customerDetail = useAppSelector(selectCustomerDetail),
    prebookDetail = useAppSelector(selectPrebookDetail),
    shipTos = useAppSelector(selectShipTos),
    emails = useAppSelector(selectEmails),
    productDefaults = useAppSelector(selectProductDefaults),
    [showInventoryDialog, setShowInventoryDialog] = useState(false),
    [showDeleteAlert, setShowDeleteAlert] = useState(false),
    [showAddSeasonDialog, setShowAddSeasonDialog] = useState(false),
    [showEmailDialog, setShowEmailDialog] = useState(false),
    [showReviewEmails, setShowReviewEmails] = useState(false),
    [showSendToSpireDialog, setShowSendToSpireDialog] = useState(false),
    requiredDateRef = useRef<HTMLInputElement | null>(null),
    shipToRef = useRef<HTMLInputElement | null>(null),
    seasonNames = (seasons || []).map((s) => s.name),
    cellClassName = 'py-2 px-1 text-left text-sm font-semibold text-gray-900',
    overbookedError =
      !isBlanket && items.some((i) => itemIsOverbooked(i, blanketItems))
        ? createProblemDetails('Please ensure no blanket items are overbooked')
        : null,
    keyMap = { ADD_ITEM: 'alt+a' },
    hotKeysHandlers = {
      ADD_ITEM: () => setShowInventoryDialog(true),
    },
    readonly = !can('Sales Team'),
    requiredDateInPastError = dateIsInPast(requiredDate)
      ? 'The Required Date is in the past'
      : null;

  configureHotkeys({ ignoreTags: [] });

  useEffect(() => {
    dispatch(clearState());
    dispatch(getBlanketItems());
  }, [dispatch]);

  useEffect(() => {
    if (typeof id === 'string' && parseQuantity(id)) {
      dispatch(getPrebookDetail(parseQuantity(id)));
    }
  }, [dispatch, id]);

  useEffect(() => {
    if (action === 'send') {
      setShowEmailDialog(true);
    }
  }, [action]);

  useEffect(() => {
    if (customer) {
      dispatch(getCustomerDetail(customer.id));
    }
  }, [dispatch, customer]);

  useEffect(() => {
    if (shipTo && !shipTos.find((st) => st.id === shipTo.id)) {
      dispatch(setShipTo(null));
      // this will happen on the initial load
    } else if (!shipTo && prebookDetail?.shipToId) {
      const shipTo =
        shipTos.find((st) => st.id === prebookDetail.shipToId) || null;
      dispatch(setShipTo(shipTo));
    }

    if (!salesperson && prebookDetail?.salespersonId) {
      const salesperson = salespeople?.find(
        (s) => s.id === prebookDetail.salespersonId
      );
      if (salesperson) {
        dispatch(setSalesperson(salesperson));
      }
    }
  }, [
    shipTo,
    customerDetail,
    salesperson,
    dispatch,
    salespeople,
    shipTos,
    prebookDetail,
  ]);

  const handleSetGrowerConfirmedClick = async () => {
    if (prebookDetail) {
      dispatch(setGrowerConfirmed(prebookDetail.id));
    }
  };

  const handleEmailClick = async () => {
    if (prebookDetail) {
      const saved = await save();
      if (saved) {
        setShowEmailDialog(true);
      }
    }
  };

  const handleEmailCancel = () => {
    setShowEmailDialog(false);
  };

  const handleEmailClose = () => {
    setShowEmailDialog(false);
  };

  const handleClearErrorClick = () => {
    dispatch(clearError());
  };

  const handleReviewEmailsClick = () => {
    setShowReviewEmails(true);
  };

  const handleReviewEmailsClose = () => {
    setShowReviewEmails(false);
  };

  const handleCustomerChange = (customer: spire.Customer | null) => {
    if (customer) {
      dispatch(getCustomerDetail(customer.id));
    }
    dispatch(setCustomer(customer));
    if (customer) {
      shipToRef.current?.focus();
    }
  };

  const handleVendorChange = (vendor: spire.Vendor | null) => {
    dispatch(setVendor(vendor));

    if (vendor) {
      requiredDateRef.current?.focus();
    }
  };

  const handleShipToChange = (
    shipTo: spire.CustomerShipTo | null,
    customer?: spire.CustomerDetail
  ) => {
    if (customer) {
      dispatch(setCustomerDetail(customer));
    }

    dispatch(setShipTo(shipTo));

    const salesperson =
      salespeople?.find((s) => s.code === shipTo?.salesperson?.code) || null;
    dispatch(setSalesperson(salesperson));
    dispatch(setBoxCode(shipTo?.boxCode || null));
  };

  const handleSalespersonChange = (salesperson: spire.Salesperson | null) => {
    dispatch(setSalesperson(salesperson));
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setName(e.target.value || ''));
  };

  const handleRequiredDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setRequiredDate(e.target.value || null));
  };

  const handleIsBlanketChange = (isBlanket: boolean) => {
    dispatch(setIsBlanket(isBlanket));
  };

  const handleBlanketStartDateChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    dispatch(setBlanketStartDate(e.target.value || null));
  };

  const handleBlanketIsClosedChange = (blanketIsClosed: boolean) => {
    dispatch(setBlanketIsClosed(blanketIsClosed));
  };

  const handleSeasonNameChange = (season: string | null) => {
    dispatch(setSeasonName(season));
  };

  const handleBoxCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setBoxCode(e.target.value || null));
  };

  const handleCommentsChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    dispatch(setComments(e.target.value || null));
  };

  const handleGrowerItemNotesChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    dispatch(setGrowerItemNotes(e.target.value || null));
  };

  const handleAddItemClick = () => {
    setShowInventoryDialog(true);
  };

  const handleAddItemConfirm = async (
    result: spire.InventoryItem,
    blanketItemId: number | null,
    comments: string | null
  ) => {
    const item: InventoryItemWithDefaults = { ...result };
    if (blanketItemId) {
      item.blanketItemId = blanketItemId;
    }
    if (comments) {
      item.comments = comments;
    }

    if (item.partNo.endsWith('PC')) {
      item.hasPotCover = true;
    } else if (noPotCover(item.partNo, item.description)) {
      item.hasPotCover = false;
      item.potCover = null;
    }
    if (customer && shipTo) {
      const { default: details } = await prebooksApi.productShipToDefaults(
        item.id,
        customer.id,
        shipTo.id
      );

      if (details.id) {
        item.customerItemCode = details.customerItemCode;
        item.hasPotCover = details.hasPotCover;
        item.potCover = details.potCover;
        item.retail = details.retail;
        item.unitPrice = details.unitPrice;
        item.upc = details.upc;
        item.weightsAndMeasures = details.weightsAndMeasures;
      }
    }
    await dispatch(addItem(item));
  };

  const handleAddItemCancel = () => {
    setShowInventoryDialog(false);
  };

  const handleDeleteClick = () => {
    setShowDeleteAlert(true);
  };

  const handleDeleteCancel = () => {
    setShowDeleteAlert(false);
  };

  const handleDeleteConfirm = async () => {
    var response: any = await dispatch(deletePrebook());

    setShowDeleteAlert(false);

    if (!response?.error) {
      if (window.history.length > 1) {
        router.back();
      } else {
        router.push(routes.prebooks.list.to());
      }
    }
  };

  const handleSendToSpireClick = () => {
    setShowSendToSpireDialog(true);
  };

  const handleSendToSpireCancel = () => {
    setShowSendToSpireDialog(false);
  };

  const handleSendToSpireConfirm = async () => {
    if (prebookDetail) {
      const saved = await save();
      if (saved) {
        const response: any = await dispatch(sendToSpire(prebookDetail.id));
        if (!response?.error) {
          if (window.history.length > 1) {
            router.back();
          } else {
            router.push(routes.prebooks.list.to());
          }
        }
      }
    }
  };

  const handleAddSeasonClick = () => {
    setShowAddSeasonDialog(true);
  };

  const handleAddSeasonCancel = () => {
    setShowAddSeasonDialog(false);
  };

  const handleAddSeasonConfirm = async (season: prebooks.Season) => {
    dispatch(setSeasonName(season.name));
    setShowAddSeasonDialog(false);
  };

  const handleBoekestynProductSaved = (
    itemId: number,
    value: BoekestynProduct[]
  ) => {
    dispatch(setItemBoekestynProducts({ itemId, value }));
  };

  const handleSaveClick = async () => {
    const saved = await save();
    if (saved) {
      if (window.history.length > 1) {
        router.back();
      } else {
        router.push(routes.prebooks.list.to());
      }
    }
  };

  const handleCancelClick = () => {
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push(routes.prebooks.list.to());
    }
  };

  const handlePrintClick = async () => {
    const saved = await save();
    if (saved) {
      window.open(apiUrl(`reports/prebooks/${id}`), '_blank');
    }
  };

  const save = async () => {
    if (readonly) {
      return true;
    }

    if (!vendor) {
      dispatch(setError(createProblemDetails('Please choose the Vendor.')));
    } else if (!requiredDate && !seasonName) {
      dispatch(
        setError(
          createProblemDetails(
            'Please enter either the Required Date or the Season'
          )
        )
      );
    } else if (
      blanketStartDate &&
      requiredDate &&
      blanketStartDate > requiredDate
    ) {
      dispatch(
        setError(
          createProblemDetails(
            'Please ensure the Blanket From date precedes the To date.'
          )
        )
      );
    } else if (!items.length) {
      dispatch(setError(createProblemDetails('Please add one or more items.')));
    } else {
      var response: any = await dispatch(updatePrebook());
      if (!response?.error) {
        return true;
      }
    }

    return false;
  };

  return (
    <div>
      <GlobalHotKeys keyMap={keyMap} handlers={hotKeysHandlers} />
      <Head>
        <title>{`Prebook #${formatNumber(id, '00000')}`}</title>
      </Head>
      <header className="sticky top-0 z-20 flex-wrap bg-white shadow">
        <div className="mx-auto px-4 py-6 sm:px-6 md:flex md:items-center md:justify-between lg:px-8">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight md:flex-shrink-0">
            <Icon icon="file-check" />
            &nbsp;
            {`Edit Prebook #${formatNumber(id, '00000')}`}
          </h2>
          {!!prebookDetail?.futureOrderId && (
            <div className="flex">
              <Link
                href={routes.futureOrders.detail.to(
                  prebookDetail.futureOrderId
                )}
                className="btn-secondary"
              >
                Future Order{' '}
                {formatNumber(prebookDetail.futureOrderId, '00000')}
              </Link>
            </div>
          )}
          <div className="flex">
            <button
              type="button"
              onClick={handleCancelClick}
              className="btn-secondary inline-flex"
            >
              {readonly ? 'Close' : 'Cancel'}
            </button>
            <button
              type="button"
              className="btn-secondary ml-3 border-blue-600 text-blue-600"
              onClick={handlePrintClick}
            >
              Print&nbsp;
              <Icon icon="print" />
            </button>
            {!readonly && (
              <>
                <button
                  type="button"
                  className="btn-secondary ml-3"
                  onClick={handleSaveClick}
                >
                  Save &amp; Close &nbsp;
                  <Icon icon="save" />
                </button>

                <div className="inline-flex rounded-md shadow-sm">
                  <button
                    onClick={handleEmailClick}
                    className={classNames(
                      'btn-primary ml-3 inline-flex',
                      !prebookDetail?.spirePurchaseOrderId && 'rounded-r-none'
                    )}
                  >
                    Save &amp; Send &nbsp;
                    <Icon icon="save" />
                    &nbsp;
                    <Icon icon="paper-plane" />
                  </button>

                  {!prebookDetail?.spirePurchaseOrderId && (
                    <HeadlessUI.Menu as="div" className="relative -ml-px block">
                      <HeadlessUI.Menu.Button className="btn-secondary relative rounded-l-none border-blue-600 px-2 text-blue-600 focus:outline-none focus:ring-0">
                        <span className="sr-only">Open options</span>
                        <Icon
                          icon="chevron-down"
                          className="h-5 w-5"
                          aria-hidden="true"
                        />
                      </HeadlessUI.Menu.Button>
                      <HeadlessUI.Transition
                        as={Fragment}
                        enter="transition ease-out duration-100"
                        enterFrom="transform opacity-0 scale-95"
                        enterTo="transform opacity-100 scale-100"
                        leave="transition ease-in duration-75"
                        leaveFrom="transform opacity-100 scale-100"
                        leaveTo="transform opacity-0 scale-95"
                      >
                        <HeadlessUI.Menu.Items className="absolute right-0 z-10 -mr-1 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                          <div className="py-1">
                            <HeadlessUI.Menu.Item>
                              {({ active }) => (
                                <button
                                  type="button"
                                  className={classNames(
                                    active
                                      ? 'bg-gray-100 text-gray-900'
                                      : 'text-gray-700',
                                    'block w-full px-4 py-2 text-left text-sm'
                                  )}
                                  onClick={handleSendToSpireClick}
                                >
                                  <Icon icon="arrow-circle-right" fixedWidth />
                                  &nbsp; Send to Spire
                                </button>
                              )}
                            </HeadlessUI.Menu.Item>
                            <HeadlessUI.Menu.Item>
                              {({ active }) => (
                                <button
                                  type="button"
                                  className={classNames(
                                    active ? 'bg-gray-100' : '',
                                    'block w-full px-4 py-2 text-left text-sm text-red-600'
                                  )}
                                  onClick={handleDeleteClick}
                                >
                                  <Icon icon="trash" fixedWidth />
                                  &nbsp; Delete
                                </button>
                              )}
                            </HeadlessUI.Menu.Item>
                          </div>
                        </HeadlessUI.Menu.Items>
                      </HeadlessUI.Transition>
                    </HeadlessUI.Menu>
                  )}
                </div>
              </>
            )}
          </div>
        </div>
      </header>
      <main className="flex-grow overflow-auto">
        <div className="mx-auto py-6 sm:px-6 lg:px-8">
          {isLoading && <Loading />}
          <Error
            error={error}
            clear={handleClearErrorClick}
            containerClasses="w-full mt-4 whitespace-pre"
          />
          <Error
            error={overbookedError}
            type="warning"
            containerClasses="w-full mt-4 whitespace-pre"
          />
          <Error
            error={requiredDateInPastError}
            type="warning"
            containerClasses="w-full mt-4 whitespace-pre"
          />
          {prebookDetail && (
            <form className="space-y-8 divide-y divide-gray-200">
              <div className="space-y-8 divide-y divide-gray-200">
                <div
                  className={classNames(
                    'grid grid-cols-1 gap-x-4 gap-y-6 lg:grid-cols-3 xl:grid-cols-4',
                    emails.length ? '' : 'mt-6'
                  )}
                >
                  {!!prebookDetail?.futureOrderId && (
                    <Error
                      containerClasses="col-start-1 col-span-1 lg:col-span-3 xl:col-span-4"
                      type="warning"
                      error={createProblemDetails(
                        'NOTE: Changes to this Prebook will not be reflected in the Future Order.'
                      )}
                    />
                  )}
                  {!emails.length && (
                    <Error
                      containerClasses="col-span-1 lg:col-span-3 xl:col-span-4"
                      type="warning"
                      error={createProblemDetails(
                        'Prebook has not been sent to the Grower'
                      )}
                    />
                  )}
                  <div className="col-span-1 xl:col-span-2">
                    {!!emails.length &&
                      !prebookDetail?.confirmed &&
                      !isLoading && (
                        <>
                          <Error
                            containerClasses="text-xs"
                            type="information"
                            error={createProblemDetails(
                              'Prebook has not been confirmed by the Grower'
                            )}
                          >
                            <div className="flex flex-grow">
                              <button
                                type="button"
                                className="btn-secondary ml-auto px-2 py-1 text-xs"
                                onClick={handleSetGrowerConfirmedClick}
                              >
                                Click to Confirm &nbsp;
                                <Icon icon="check-circle" />
                              </button>
                            </div>
                          </Error>
                        </>
                      )}
                    {!!prebookDetail?.confirmed && (
                      <p className="text-xs text-green-700 ">
                        Confirmed by Grower by&nbsp;
                        <span className="italic">
                          {prebookDetail?.confirmedBy}
                        </span>
                        &nbsp;on&nbsp;
                        <span className="italic">
                          {formatDate(prebookDetail?.confirmed, 'MMM d, yyyy')}
                        </span>
                        &nbsp;@&nbsp;
                        <span className="italic">
                          {formatDate(prebookDetail?.confirmed, 'h:mm a')}
                        </span>
                      </p>
                    )}
                  </div>
                  {!!emails.length && (
                    <div className="col-span-1 col-start-1 text-right lg:col-span-2 lg:col-start-2 xl:col-start-3">
                      {emails.map((email) => (
                        <p key={email.id} className="text-xs text-green-700 ">
                          Sent to supplier by&nbsp;
                          <span className="italic">{email.createdBy}</span>
                          &nbsp;on&nbsp;
                          <span className="italic">
                            {formatDate(email.created, 'MMM d, yyyy')}
                          </span>
                          &nbsp;@&nbsp;
                          <span className="italic">
                            {formatDate(email.created, 'h:mm a')}
                          </span>
                        </p>
                      ))}
                      <button
                        type="button"
                        onClick={handleReviewEmailsClick}
                        className="text-xs text-blue-500 underline hover:no-underline"
                      >
                        <Icon icon="glasses"></Icon>
                        &nbsp; Review emails
                      </button>
                    </div>
                  )}
                  <div className="col-start-1">
                    <div className="mt-1 rounded-md shadow-sm">
                      <Combobox
                        value={vendor}
                        onChange={handleVendorChange}
                        label="Vendor"
                        collection={vendors}
                        filter={(q, v) => startsWith(v.name, q)}
                        disabled={readonly}
                        secondaryDisplayTextProp="vendorNo"
                        autofocus
                      />
                    </div>
                  </div>
                  {!isBlanket && (
                    <div>
                      <label
                        htmlFor="required-date"
                        className="block text-sm font-medium text-gray-500"
                      >
                        Required Date &nbsp;
                        <span className="text-red-500">&nbsp;*</span>
                      </label>
                      <div className="mt-1">
                        <input
                          type="date"
                          max="2050-01-01"
                          name="requiredDate"
                          id="required-date"
                          value={requiredDate || ''}
                          onChange={handleRequiredDateChange}
                          disabled={readonly}
                          ref={requiredDateRef}
                          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        />
                      </div>
                    </div>
                  )}
                  {isBlanket && (
                    <div>
                      <div className="grid grid-cols-2">
                        <div>
                          <label
                            htmlFor="blanket-start-date"
                            className="block text-sm font-medium text-gray-500"
                          >
                            Blanket From
                          </label>
                        </div>
                        <div>
                          <label
                            htmlFor="required-date"
                            className="block text-sm font-medium text-gray-500"
                          >
                            To
                            <span className="text-red-500">&nbsp;*</span>
                          </label>
                        </div>
                        <div className="col-start-1 mt-1">
                          <input
                            type="date"
                            max="2050-01-01"
                            name="blanketStartDate"
                            id="blanket-start-date"
                            value={blanketStartDate || ''}
                            onChange={handleBlanketStartDateChange}
                            disabled={readonly}
                            ref={requiredDateRef}
                            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                          />
                        </div>
                        <div className="mt-1">
                          <input
                            type="date"
                            max="2050-01-01"
                            name="requiredDate"
                            id="required-date"
                            value={requiredDate || ''}
                            onChange={handleRequiredDateChange}
                            disabled={readonly}
                            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                          />
                        </div>
                      </div>
                    </div>
                  )}
                  <div>
                    <label
                      htmlFor="is-blanket"
                      className="block text-sm font-medium text-gray-500"
                    >
                      Prebook is a Blanket
                    </label>
                    <div className="mt-1 h-10">
                      <HeadlessUI.Switch
                        className={classNames(
                          isBlanket ? 'bg-blue-600' : 'bg-gray-200',
                          'relative mt-2 inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                        )}
                        onChange={handleIsBlanketChange}
                        disabled={readonly}
                        checked={isBlanket}
                      >
                        <span
                          aria-hidden="true"
                          className={classNames(
                            isBlanket ? 'translate-x-5' : 'translate-x-0',
                            'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                          )}
                        />
                      </HeadlessUI.Switch>
                    </div>
                  </div>
                  {isBlanket && (
                    <div>
                      <label
                        htmlFor="blanket-is-closed"
                        className="block text-sm font-medium text-gray-500"
                      >
                        Blanket is Closed
                      </label>
                      <div className="mt-1 h-9">
                        <HeadlessUI.Switch
                          id="blanket-is-closed"
                          className={classNames(
                            blanketIsClosed ? 'bg-blue-600' : 'bg-gray-200',
                            'relative mt-3 inline-flex h-5 w-10 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                          )}
                          onChange={handleBlanketIsClosedChange}
                          disabled={readonly}
                          checked={blanketIsClosed}
                        >
                          <span
                            aria-hidden="true"
                            className={classNames(
                              blanketIsClosed
                                ? 'translate-x-5'
                                : 'translate-x-0',
                              'pointer-events-none inline-block h-4 w-4 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                            )}
                          />
                        </HeadlessUI.Switch>
                      </div>
                    </div>
                  )}
                  <div className="col-start-1">
                    <div className="mt-1 rounded-md shadow-sm">
                      <Combobox
                        value={customer}
                        onChange={handleCustomerChange}
                        label="Customer"
                        collection={customers}
                        filter={(q, c) =>
                          startsWith(c.name, q) || startsWith(c.customerNo, q)
                        }
                        disabled={readonly}
                        secondaryDisplayTextProp="customerNo"
                        nullDisplayText="No Customer"
                      />
                    </div>
                  </div>
                  <div className="col-auto">
                    <div className="mt-1 rounded-md shadow-sm">
                      <ShipToCombobox
                        value={shipTo}
                        onChange={handleShipToChange}
                        inputRef={shipToRef}
                        customer={customerDetail}
                        disabled={readonly}
                      />
                    </div>
                  </div>
                  <div className="col-auto">
                    <div className="mt-1 rounded shadow-sm">
                      <Combobox
                        value={salesperson}
                        onChange={handleSalespersonChange}
                        label="Salesperson"
                        collection={salespeople}
                        filter={(q, s) => startsWith(s.name, q)}
                        disabled={readonly}
                      />
                    </div>
                  </div>
                  <div className="col-start-1">
                    <label
                      htmlFor="prebook-name"
                      className="block text-sm font-medium text-gray-500"
                    >
                      Name (optional)
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        id="prebook-name"
                        name="name"
                        value={name || ''}
                        onChange={handleNameChange}
                        disabled={readonly}
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        placeholder="e.g. Easter"
                      />
                    </div>
                  </div>
                  <div className="col-auto">
                    <label
                      htmlFor="box-code"
                      className="block text-sm font-medium text-gray-500"
                    >
                      Box Code
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="boxCode"
                        id="box-code"
                        value={boxCode || ''}
                        onChange={handleBoxCodeChange}
                        disabled={readonly}
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </div>
                  </div>
                  <div className="col-auto">
                    <div className="flex align-bottom">
                      <div className="flex-grow rounded shadow-sm">
                        <Combobox
                          value={seasonName}
                          onChange={handleSeasonNameChange}
                          label="Season / Holiday"
                          collection={seasonNames}
                          filter={(q, s) => startsWith(s, q)}
                          disabled={readonly}
                        />
                      </div>
                      {!readonly && (
                        <div className="mt-auto flex">
                          <button
                            type="button"
                            className="btn-secondary"
                            onClick={handleAddSeasonClick}
                            tabIndex={-1}
                          >
                            <Icon icon="plus" />
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="col-span-2">
                    <label
                      htmlFor="comments"
                      className="block text-sm font-medium text-gray-500"
                    >
                      Comments
                    </label>
                    <textarea
                      rows={4}
                      name="comments"
                      id="comment"
                      value={comments || ''}
                      onChange={handleCommentsChange}
                      disabled={readonly}
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="grower-item-notes"
                      className="block text-sm font-medium text-gray-500"
                    >
                      Grower Item Notes&nbsp;
                      <HeadlessUI.Popover className="relative inline-block">
                        <HeadlessUI.Popover.Button
                          className="btn-secondary mb-1 rounded-full px-1 py-0 text-sm text-blue-500 focus:ring-0"
                          tabIndex={-1}
                        >
                          <Icon icon="question-circle" />
                        </HeadlessUI.Popover.Button>
                        <HeadlessUI.Transition
                          as={Fragment}
                          enter="transition ease-out duration-200"
                          enterFrom="opacity-0 translate-y-1"
                          enterTo="opacity-100 translate-y-0"
                          leave="transition ease-in duration-150"
                          leaveFrom="opacity-100 translate-y-0"
                          leaveTo="opacity-0 translate-y-1"
                        >
                          <HeadlessUI.Popover.Panel className="absolute bottom-10 z-10 w-96 -translate-x-1/2 transform px-4">
                            <div className="rounded-lg bg-yellow-50 p-4 font-normal shadow-lg ring-1 ring-black ring-opacity-5">
                              Grower Item notes will be included on{' '}
                              <span className="font-semibold italic">
                                each item
                              </span>{' '}
                              of the Prebook.
                            </div>
                          </HeadlessUI.Popover.Panel>
                        </HeadlessUI.Transition>
                      </HeadlessUI.Popover>
                    </label>
                    <textarea
                      rows={4}
                      name="growerItemNotes"
                      id="grower-item-notes"
                      value={growerItemNotes || ''}
                      onChange={handleGrowerItemNotesChange}
                      disabled={readonly}
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    />
                  </div>
                  <div className="col-span-1 lg:col-span-3 xl:col-span-4">
                    <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                      <table className="min-w-full">
                        <thead className="bg-gray-50">
                          <tr>
                            <th
                              className={classNames(
                                cellClassName,
                                'pl-4 text-xs'
                              )}
                            >
                              Product
                            </th>
                            {!!emails.length && (
                              <th
                                className={classNames(
                                  cellClassName,
                                  'text-center'
                                )}
                              >
                                Prev Qty
                              </th>
                            )}
                            <th
                              className={classNames(
                                cellClassName,
                                'text-center'
                              )}
                            >
                              Order Qty
                            </th>
                            <th
                              className={classNames(
                                cellClassName,
                                'text-center'
                              )}
                            >
                              Pot Cover
                            </th>
                            <th
                              className={classNames(
                                cellClassName,
                                'text-center'
                              )}
                            >
                              UPC
                            </th>
                            <th
                              className={classNames(
                                cellClassName,
                                'text-center'
                              )}
                            >
                              Date Code
                            </th>
                            <th
                              className={classNames(
                                cellClassName,
                                'text-center'
                              )}
                            >
                              Retail
                            </th>
                            <th
                              className={classNames(
                                cellClassName,
                                'text-center'
                              )}
                            >
                              W & M
                            </th>
                            <th className={cellClassName}>&nbsp;</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white">
                          {items.map((item) => (
                            <Item key={item.id} item={item} />
                          ))}
                        </tbody>
                        <tfoot>
                          <tr>
                            <td
                              colSpan={7}
                              className={classNames(cellClassName, 'pr-4')}
                            >
                              {!readonly && (
                                <button
                                  type="button"
                                  className="btn-new"
                                  onClick={handleAddItemClick}
                                >
                                  <Icon icon="plus" />
                                  &nbsp; Add Item
                                </button>
                              )}
                            </td>
                          </tr>
                        </tfoot>
                      </table>
                    </div>
                  </div>
                  <div className="col-span-1 col-start-1 text-xs lg:col-span-3 xl:col-span-4">
                    <div>
                      Created by&nbsp;
                      <span className="italic">{prebookDetail.createdBy}</span>
                      &nbsp;on&nbsp;
                      <span className="italic">
                        {formatDate(prebookDetail.created, 'MMM d, yyyy')}
                      </span>
                      &nbsp;@&nbsp;
                      <span className="italic">
                        {formatDate(prebookDetail.created, 'h:mm a')}
                      </span>
                    </div>
                    {prebookDetail.created !== prebookDetail.modified && (
                      <div>
                        Updated by&nbsp;
                        <span className="italic">
                          {prebookDetail.modifiedBy}
                        </span>
                        &nbsp;on&nbsp;
                        <span className="italic">
                          {formatDate(prebookDetail.modified, 'MMM d, yyyy')}
                        </span>
                        &nbsp;@&nbsp;
                        <span className="italic">
                          {formatDate(prebookDetail.modified, 'h:mm a')}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </form>
          )}
        </div>
      </main>
      <Alert
        title="Delete Prebook"
        message="Are you sure you want to delete this Prebook?"
        confirmButtonText="Yes"
        cancelButtonText="No"
        colour="danger"
        open={showDeleteAlert}
        cancel={handleDeleteCancel}
        confirm={handleDeleteConfirm}
      />
      <Alert
        title="Send to Spire"
        message="This will create a Purchase Order in Spire. Would you like to continue?"
        icon="question-circle"
        confirmButtonText="Yes"
        cancelButtonText="No"
        colour="info"
        open={showSendToSpireDialog}
        cancel={handleSendToSpireCancel}
        confirm={handleSendToSpireConfirm}
      />
      <Inventory
        open={showInventoryDialog}
        cancel={handleAddItemCancel}
        confirm={handleAddItemConfirm}
        vendor={vendor}
      />
      <CreateSeason
        open={showAddSeasonDialog}
        cancel={handleAddSeasonCancel}
        confirm={handleAddSeasonConfirm}
      />
      {!!prebookDetail && showEmailDialog && (
        <Email
          prebook={prebookDetail}
          open={showEmailDialog}
          cancel={handleEmailCancel}
          close={handleEmailClose}
          saveAndNewLink={routes.prebooks.new.to()}
          saveAndCloseLink={routes.prebooks.list.to()}
        />
      )}
      {!!prebookDetail && showReviewEmails && (
        <EmailReview
          open={showReviewEmails}
          close={handleReviewEmailsClose}
          emails={emails}
        />
      )}
      <BoekestynProducts
        onSave={handleBoekestynProductSaved}
        productDefaults={productDefaults}
      />
    </div>
  );
}
