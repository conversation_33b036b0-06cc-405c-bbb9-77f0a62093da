import React, { Fragment, useEffect, useState } from 'react';
import * as HeadlessUI from '@headlessui/react';
import * as futureOrders from 'api/models/future-orders';
import { useAppDispatch } from '@/services/hooks';
import { Icon } from '@/components/icon';
import { formatCurrency } from '@/utils/format';
import {
  SetItemArgs,
  setItemPropertyValue,
  updateUpgradeOption,
} from './upgrade-item-list-slice';

interface ItemUpgradeOptionDialogProps {
  item: futureOrders.UpgradeItem;
  value: string;
  onClose: (success?: boolean) => void;
}

export function ItemUpgradeOptionDialog({
  item,
  value,
  onClose,
}: ItemUpgradeOptionDialogProps) {
  const dispatch = useAppDispatch(),
    [containerPickDescription, setContainerPickDescription] = useState(''),
    [origins, setOrigins] = useState(''),
    [costs, setCosts] = useState('');

  useEffect(() => {
    setContainerPickDescription(value);
    setOrigins(item.origins || '');
    setCosts(item.costs ? formatCurrency(item.costs) : '');
  }, [item, value]);

  const handleContainerPickDescriptionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    setContainerPickDescription(e.target.value);
  };

  const handleOriginsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setOrigins(e.target.value);
  };

  const handleCostsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCosts(e.target.value);
  };

  const handleSaveClick = async () => {
    const args = { containerPickDescription, origins, costs },
      result = await dispatch(updateUpgradeOption(args));

    if (!('error' in result)) {
      const args1: SetItemArgs = {
          id: item.id,
          propName: 'containerPickDescription',
          value: containerPickDescription,
        },
        args2: SetItemArgs = {
          id: item.id,
          propName: 'origins',
          value: origins,
        },
        args3: SetItemArgs = {
          id: item.id,
          propName: 'costs',
          value: costs,
        };

      await dispatch(setItemPropertyValue(args1));
      await dispatch(setItemPropertyValue(args2));
      await dispatch(setItemPropertyValue(args3));

      onClose(true);
    }
  };

  const handleCancelClick = () => {
    onClose();
  };

  return (
    <HeadlessUI.Transition.Root show={true} as={Fragment}>
      <HeadlessUI.Dialog as="div" className="relative z-10" onClose={onClose}>
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-0 text-center">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-0 scale-95"
              enterTo="opacity-100 translate-y-0 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 scale-100"
              leaveTo="opacity-0 translate-y-0 scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative my-8 w-full max-w-sm transform overflow-hidden rounded-lg bg-white p-6 text-left shadow-xl transition-all">
                <div>
                  <div className="mt-3 text-center">
                    <HeadlessUI.Dialog.Title
                      as="h3"
                      className="text-base font-semibold leading-6 text-gray-900"
                    >
                      Set Upgrade Option
                    </HeadlessUI.Dialog.Title>
                    <form className="mt-5 flex items-center">
                      <div className="grid w-full max-w-xs grid-cols-2 gap-2">
                        <div className="col-span-2">
                          <label
                            htmlFor="container-pick-description"
                            className="block text-left"
                          >
                            Container / Pick Description
                          </label>
                          <textarea
                            id="container-pick-description"
                            value={containerPickDescription}
                            onChange={handleContainerPickDescriptionChange}
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                          ></textarea>
                        </div>
                        <div>
                          <label htmlFor="origins" className="block text-left">
                            Origins
                          </label>
                          <input
                            id="origins"
                            type="text"
                            value={origins}
                            onChange={handleOriginsChange}
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                          />
                        </div>
                        <div>
                          <label htmlFor="costs" className="block text-left">
                            Costs
                          </label>
                          <input
                            id="costs"
                            type="text"
                            value={costs}
                            onChange={handleCostsChange}
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                          />
                        </div>
                      </div>
                    </form>
                  </div>
                </div>
                <div className="mt-6 text-right">
                  <button
                    type="button"
                    className="btn-secondary"
                    onClick={handleCancelClick}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={handleSaveClick}
                    className="btn-primary ml-2"
                  >
                    Save &nbsp;
                    <Icon icon="save" />
                  </button>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}
