import { useRef, useState, useEffect } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import {
  useDeleteStickingWorkOrderMutation,
  useSortStickingWorkOrdersMutation,
  useUpdateStickingWorkOrderCommentMutation,
  useUpdateStickingWorkOrderCrewSizeMutation,
  useStickingOrdersQuery,
  useUpdateStickingWorkOrderMutation,
} from 'api/boekestyn-sticking-service';
import * as models from 'api/models/boekestyns';
import { Icon } from '@/components/icon';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { handleFocus } from '@/utils/focus';
import { formatNumber } from '@/utils/format';
import { selectStartDate, selectEndDate } from './admin-slice';
import { moveItem, SortStickingWorkOrderType } from './sticking-slice';
import { StickingWorkOrderDialog } from './sticking-work-order-dialog';

interface StickingScheduleWorkOrderProps {
  scheduleId: number;
  order: models.StickingWorkOrder;
}

export function StickingScheduleWorkOrder({
  scheduleId,
  order,
}: StickingScheduleWorkOrderProps) {
  const dispatch = useAppDispatch(),
    startDate = useAppSelector(selectStartDate),
    endDate = useAppSelector(selectEndDate),
    [comment, setComment] = useState(order.stickingComments ?? ''),
    [crewSize, setCrewSize] = useState(order.crewSize),
    [showDetailDialog, setShowDetailDialog] = useState(false),
    [deleteWorkOrder] = useDeleteStickingWorkOrderMutation(),
    [sortWorkOrders] = useSortStickingWorkOrdersMutation(),
    [updateWorkOrderComment] = useUpdateStickingWorkOrderCommentMutation(),
    [updateWorkOrderCrewSize] = useUpdateStickingWorkOrderCrewSizeMutation(),
    [updateWorkOrder] = useUpdateStickingWorkOrderMutation(),
    ref = useRef<HTMLTableRowElement>(null),
    cellClassName = 'whitespace-nowrap px-2 py-1 text-gray-700 align-top',
    [, drag] = useDrag(() => ({
      type: `${SortStickingWorkOrderType}-${scheduleId}`,
      item: order,
    })),
    [{ isOver }, drop] = useDrop<
      models.StickingWorkOrder,
      void,
      { isOver: boolean }
    >(() => ({
      accept: `${SortStickingWorkOrderType}-${scheduleId}`,
      collect: (monitor) => ({
        isOver: monitor.isOver(),
      }),
      drop(droppedItem) {
        dispatch(
          moveItem({ scheduleId, existingItem: order, movingItem: droppedItem })
        ).then(({ payload }) => {
          const workOrders = payload as models.StickingWorkOrder[] | undefined;
          if (workOrders) {
            const args = {
              workOrders: workOrders.map((wo) => ({
                workOrderId: wo.id,
                sortOrder: wo.sortOrder,
              })),
            };
            sortWorkOrders(args);
          }
        });
      },
    })),
    { refetch: refetchOrders } = useStickingOrdersQuery({
      startDate,
      endDate,
    });

  useEffect(() => {
    setCrewSize(order.crewSize);
  }, [order.crewSize]);

  useEffect(() => {
    setComment(order.stickingComments ?? '');
  }, [order.stickingComments]);

  const handleDelete = async () => {
    await deleteWorkOrder({ id: order.id, orderId: order.orderId });
    refetchOrders();
  };

  const handleCommentsChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setComment(event.target.value);
  };

  const handleCommentsBlur = async () => {
    if (comment !== order.stickingComments) {
      const args = {
        id: order.id,
        comment,
      };
      await updateWorkOrderComment(args);
    }
  };

  const handleCrewSizeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(event.target.value, 10);
    setCrewSize(value);
  };

  const handleCrewSizeBlur = async () => {
    if (crewSize !== order.crewSize) {
      const args = {
        id: order.id,
        crewSize,
      };
      await updateWorkOrderCrewSize(args);
    }
  };

  const handleLotNumberClick = () => {
    setShowDetailDialog(true);
  };
 
  const handleDetailDialogSave = (data: { crewSize?: number, flowerDate?: string; varietyQuantities?: { name: string; quantity: number }[] }) => {
    const args = {
      id: order.id,
      crewSize: data.crewSize ?? 0,
      flowerDate: data.flowerDate ?? null,
      varietyQuantities: data.varietyQuantities ?? [],
    };
    updateWorkOrder(args);
    setShowDetailDialog(false);
  };

  drag(drop(ref));

  return (
    <>
      <tr
        ref={ref}
        className={classNames(
          'border-gray-200',
          isOver && 'border-t-4 border-t-gray-500'
        )}
      >
        <td className={cellClassName}>
          <button
            type="button"
            className="btn-link"
            onClick={handleLotNumberClick}
          >
            {order.orderNumber}
          </button>
          {!!order.orderComments && (
            <div className="italic">{order.orderComments}</div>
          )}
        </td>
        <td className={cellClassName}>
          <span className="font-semibold">
            {order.plantSize}&nbsp;{order.plantCrop}&nbsp;{order.customer}
          </span>
          <div className="pl-4 italic">
            {order.varieties.map((variety) => (
              <div key={variety.name}>{variety.name}</div>
            ))}
          </div>
        </td>
        <td className={classNames(cellClassName, 'text-right')}>
          <span className="font-semibold">{formatNumber(order.pots)}</span>
          <div className="pl-4 italic">
            {order.varieties.map((variety) => (
              <div key={variety.name}>{formatNumber(variety.pots)}</div>
            ))}
          </div>
        </td>
        <td className={classNames(cellClassName, 'text-right')}>
          {formatNumber(order.estimatedHours, '0,0.0')}
        </td>
        <td className={classNames(cellClassName, 'text-center')}>
          <input
            type="number"
            className="w-24 rounded-md border-gray-300 text-right text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
            value={crewSize}
            onFocus={handleFocus}
            onChange={handleCrewSizeChange}
            onBlur={handleCrewSizeBlur}
          />
        </td>
        <td className={classNames(cellClassName, 'text-right')}>
          {formatNumber(order.estimatedHours * order.crewSize, '0,0.0')}
        </td>
        <td className={classNames(cellClassName, 'w-1 text-center')}>
          <div className="flex flex-row">
            <div
              className="btn-secondary h-8 w-8 cursor-pointer px-2 py-1 text-center"
              // @ts-ignore
              ref={drag}
            >
              <Icon icon="arrows-up-down" />
            </div>
            <button
              type="button"
              className="delete p-1 text-red-500"
              onClick={handleDelete}
            >
              <Icon icon="trash" />
            </button>
          </div>
        </td>
      </tr>
      <tr className="border-b border-gray-200">
        <td className={cellClassName} colSpan={8}>
          <label className="block text-xs italic">Schedule Notes</label>
          <input
            type="text"
            className="mb-2 w-full rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
            value={comment ?? ''}
            onChange={handleCommentsChange}
            onBlur={handleCommentsBlur}
          />
        </td>
      </tr>
      <StickingWorkOrderDialog
        open={showDetailDialog}
        onClose={() => {setShowDetailDialog(false);}}
        onSave={handleDetailDialogSave}
        order={order}
      />
    </>
  );
}
