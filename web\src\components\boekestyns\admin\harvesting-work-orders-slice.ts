import moment from 'moment';
import { createSelector, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { boekestynHarvestingApi } from 'api/boekestyn-harvesting-service';
import * as models from 'api/models/boekestyns';
import { RootState } from '@/services/store';
import { sortBy } from '@/utils/sort';
import { ProblemDetails } from '@/utils/problem-details';

const sortByDate = sortBy('date');

export interface ScheduleData {
  id: number;
  date: string;
  workOrder: WorkOrderData;
}

export interface WorkOrderData {
  stickingWorkOrderId: number;
  id: number;
  crewSize: number;
  finalRound: boolean;
  harvestingComments: string | null;
  varieties: VarietyData[];
  defaultExpectedHarvestPercentage: number;
}

export interface VarietyData extends models.HarvestingWorkOrderVariety {
  selected: boolean;
  expectedHarvestPercentage: number;
}

interface HarvestingWorkOrdersState {
  selectedTab: number;
  selectedOrder: models.HarvestingAdminOrderItem | null;
  schedules: ScheduleData[];
  error: ProblemDetails | null;
}

const initialWorkOrderData: WorkOrderData = {
  id: 0,
  crewSize: 1,
  finalRound: false,
  harvestingComments: null,
  varieties: [],
  defaultExpectedHarvestPercentage: 100,
  stickingWorkOrderId: 0,
};

const initialState: HarvestingWorkOrdersState = {
  selectedTab: 0,
  selectedOrder: null,
  schedules: [
    {
      id: 0,
      date: moment().format('YYYY-MM-DD'),
      workOrder: { ...initialWorkOrderData },
    },
  ],
  error: null,
};

const harvestingWorkOrdersSlice = createSlice({
  name: 'harvestingWorkOrders',
  initialState,
  reducers: {
    setSelectedOrder(
      state,
      { payload }: PayloadAction<models.HarvestingAdminOrderItem | null>
    ) {
      state.selectedOrder = payload;
    },
    setSelectedTab(state, { payload }: PayloadAction<number>) {
      state.selectedTab = payload;
    },
    setScheduleDate(
      state,
      { payload }: PayloadAction<{ scheduleId: number; date: string }>
    ) {
      const schedules = state.schedules.map((date) => ({ ...date })),
        schedule = schedules.find((s) => s.id === payload.scheduleId);
      if (schedule) {
        schedule.date = payload.date;
      }
      state.schedules = schedules.sort(sortByDate);
    },
    setDefaultExpectedHarvestPercentage(
      state,
      action: PayloadAction<{ workOrderId: number; percentage: number }>
    ) {
      const schedules = state.schedules.map((date) => ({ ...date })),
        schedule = schedules.find(
          (s) => s.workOrder.id === action.payload.workOrderId
        );
      if (schedule) {
        schedule.workOrder.defaultExpectedHarvestPercentage =
          action.payload.percentage;
      }
      state.schedules = schedules;
    },
    setCrewSize(
      state,
      action: PayloadAction<{ id: number; crewSize: number }>
    ) {
      const schedules = state.schedules.map((date) => ({ ...date })),
        schedule = schedules.find((s) => s.id === action.payload.id);
      if (schedule) {
        schedule.workOrder.crewSize = action.payload.crewSize;
      }
      state.schedules = schedules;
    },
    setComments(
      state,
      action: PayloadAction<{ id: number; comments: string | null }>
    ) {
      const schedules = state.schedules.map((date) => ({ ...date })),
        schedule = schedules.find((s) => s.id === action.payload.id);
      if (schedule) {
        schedule.workOrder.harvestingComments = action.payload.comments;
      }
      state.schedules = schedules;
    },
    addWorkOrderDate(state, { payload }: PayloadAction<string>) {
      const date = payload,
        order = state.selectedOrder;
      if (order && !state.schedules.some((d) => d.date === date)) {
        const schedules = state.schedules.map((d) => ({ ...d })),
          id = schedules.reduce((min, d) => Math.min(min, d.id), 0) - 1,
          workOrderId =
            schedules.reduce((min, d) => Math.min(min, d.workOrder.id), 0) - 1,
          crewSize = schedules[schedules.length - 1]?.workOrder.crewSize || 1;
        schedules.push({
          id,
          date,
          workOrder: {
            ...initialWorkOrderData,
            id: workOrderId,
            crewSize,
            varieties: order.varieties.map((v, i) => ({
              id: -i,
              workOrderId,
              name: v.name,
              pots: v.pots,
              expectedHarvestPercentage: 100,
              comment: null,
              selected: true,
            })),
          },
        });
        schedules.sort(sortByDate);
        schedules.forEach(
          (s, i) => (s.workOrder.finalRound = i === schedules.length - 1)
        );
        state.schedules = schedules;
      }
    },
    removeWorkOrderDate(state, { payload }: PayloadAction<number>) {
      if (state.schedules.length > 1) {
        const index = state.schedules.findIndex((d) => d.id === payload);
        state.schedules = state.schedules.filter((d) => d.id !== payload);

        if (index === state.selectedTab) {
          state.selectedTab = Math.max(0, index - 1);
        }
      }
    },
    setVarietySelection(
      state,
      action: PayloadAction<{
        workOrderId: number;
        varietyName: string;
        selected: boolean;
      }>
    ) {
      const schedules = state.schedules.map((date) => ({ ...date })),
        index = schedules.findIndex(
          (s) => s.workOrder.id === action.payload.workOrderId
        );

      if (index !== -1) {
        const schedule = {
          ...schedules[index],
          workOrder: {
            ...schedules[index].workOrder,
            varieties: schedules[index].workOrder.varieties.map((variety) => ({
              ...variety,
              expectedHarvestPercentage:
                variety.name === action.payload.varietyName
                  ? action.payload.selected
                    ? schedules[index].workOrder
                        .defaultExpectedHarvestPercentage
                    : 0
                  : variety.expectedHarvestPercentage,
              selected:
                variety.name === action.payload.varietyName
                  ? action.payload.selected
                  : variety.selected,
            })),
          },
        };
        schedules[index] = schedule;
      }

      state.schedules = schedules;
    },
    setVarietyExpectedPercentages(
      state,
      action: PayloadAction<{
        workOrderId: number;
        percentage: number;
      }>
    ) {
      const schedules = state.schedules.map((date) => ({ ...date })),
        index = schedules.findIndex(
          (s) => s.workOrder.id === action.payload.workOrderId
        );

      if (index !== -1) {
        const schedule = {
          ...schedules[index],
          workOrder: {
            ...schedules[index].workOrder,
            defaultExpectedHarvestPercentage: action.payload.percentage,
            varieties: schedules[index].workOrder.varieties.map((variety) => ({
              ...variety,
              expectedHarvestPercentage: variety.selected
                ? action.payload.percentage
                : 0,
            })),
          },
        };
        schedules[index] = schedule;
      }

      state.schedules = schedules;
    },
    setVarietyExpectedPercentage(
      state,
      action: PayloadAction<{
        workOrderId: number;
        varietyName: string;
        percentage: number;
      }>
    ) {
      const schedules = state.schedules.map((date) => ({ ...date })),
        index = schedules.findIndex(
          (s) => s.workOrder.id === action.payload.workOrderId
        );

      if (index !== -1) {
        const schedule = {
          ...schedules[index],
          workOrder: {
            ...schedules[index].workOrder,
            varieties: schedules[index].workOrder.varieties.map((variety) => ({
              ...variety,
              expectedHarvestPercentage:
                variety.name === action.payload.varietyName
                  ? action.payload.percentage
                  : variety.expectedHarvestPercentage,
            })),
          },
        };
        schedules[index] = schedule;
      }

      state.schedules = schedules;
    },
    resetWorkOrders(
      state,
      { payload }: PayloadAction<models.HarvestingAdminOrderItem | null>
    ) {
      const { schedules } = {
          ...initialState,
        },
        schedule = schedules[0];

      if (schedule) {
        const intitial = {
          ...state.schedules[0],
          workOrder: {
            ...schedule.workOrder,
            varieties:
              payload?.varieties.map((v, i) => ({
                id: -i,
                workOrderId: schedule.workOrder.id,
                name: v.name,
                pots: v.pots,
                expectedHarvestPercentage: 100,
                comment: null,
                selected: true,
              })) || [],
          },
        };
        state.schedules = [intitial];
        state.selectedOrder = payload;
        state.error = null;
        state.selectedTab = 0;
      }
    },
    setError(state, { payload }: PayloadAction<ProblemDetails | null>) {
      state.error = payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addMatcher(
        boekestynHarvestingApi.endpoints.schedulesForOrder.matchFulfilled,
        (state, { payload }) => {
          state.error = null;
          state.selectedOrder = payload.order;
          state.schedules = payload.schedules.map((schedule) => ({
            id: schedule.id,
            date: schedule.date,
            lineId: schedule.lineId,
            workOrder: {
              id: schedule.workOrders[0].id,
              crewSize: schedule.workOrders[0].crewSize,
              finalRound: schedule.workOrders[0].finalRound,
              harvestingComments: schedule.workOrders[0].harvestingComments,
              defaultExpectedHarvestPercentage:
                schedule.workOrders[0].defaultExpectedHarvestPercentage,
              stickingWorkOrderId: schedule.workOrders[0].stickingWorkOrderId,
              varieties: payload.order.varieties.map((v, i) => {
                const variety = schedule.workOrders[0].varieties.find(
                  (sv) => sv.name === v.name
                );

                return {
                  id: variety?.id ?? -i,
                  workOrderId: schedule.workOrders[0].id,
                  name: v.name,
                  pots: v.pots,
                  expectedHarvestPercentage:
                    variety?.expectedHarvestPercentage ?? 0,
                  comment: v?.comment,
                  selected: !!variety,
                };
              }),
            },
          }));
          state.selectedTab = 0;
        }
      )
      .addMatcher(
        boekestynHarvestingApi.endpoints.schedulesForOrder.matchRejected,
        (state, { payload }) => {
          state.error = payload as ProblemDetails;
        }
      );
  },
});

export const {
  setSelectedOrder,
  setSelectedTab,
  setScheduleDate,
  setCrewSize,
  setDefaultExpectedHarvestPercentage,
  setComments,
  addWorkOrderDate,
  removeWorkOrderDate,
  setVarietySelection,
  setVarietyExpectedPercentage,
  setVarietyExpectedPercentages,
  resetWorkOrders,
  setError,
} = harvestingWorkOrdersSlice.actions;

export const selectSelectedOrder = ({ harvestingWorkOrders }: RootState) =>
  harvestingWorkOrders.selectedOrder;
export const selectSelectedTab = ({ harvestingWorkOrders }: RootState) =>
  harvestingWorkOrders.selectedTab;
const selectAllSchedules = ({ harvestingWorkOrders }: RootState) =>
  harvestingWorkOrders.schedules;
export const selectError = ({ harvestingWorkOrders }: RootState) =>
  harvestingWorkOrders.error;

export const selectSchedules = createSelector(selectAllSchedules, (schedules) =>
  schedules.map((s) => ({ ...s })).sort(sortByDate)
);

export default harvestingWorkOrdersSlice.reducer;
