import { useState, Fragment, useEffect, useMemo } from 'react';
import * as HeadlessUI from '@headlessui/react';
import * as boeks from 'api/models/boekestyns';
import * as models from 'api/models/boekestyns';
import { classNames } from '@/utils/class-names';
import {
  useLazySchedulesForOrderQuery,
  useHarvestingLabourQuery,
  HarvestingLabourVariety,
} from 'api/boekestyn-harvesting-service';
import { formatDate } from '@/utils/format';
import { RecordLabourDialogItem } from './record-labour-dialog-item';

interface PauseLabourDialogProps {
  workOrder: boeks.HarvestingWorkOrderItem;
  open: boolean;
  onClose: () => void;
  onRecord: (args: {
    comments: string | null;
    crewSize: number;
    varieties: HarvestingLabourVariety[];
    finalHarvest: boolean;
  }) => void;
}

export function RecordLabourDialog({
  workOrder,
  open,
  onClose,
  onRecord,
}: PauseLabourDialogProps) {
  const [comments, setComments] = useState('');
  const [finalHarvest, setFinalHarvest] = useState(false);
  const [crewSize, setCrewSize] = useState('1');
  const [harvestingOrder, setHarvestingOrder] =
    useState<models.HarvestingAdminOrderItem | null>(null);
  const { data: harvestingLabour } = useHarvestingLabourQuery(
    workOrder.orderId,
    { skip: !workOrder.orderId }
  );
  const history = useMemo(
    () => harvestingLabour?.labour ?? [],
    [harvestingLabour]
  );

  // Fetch the harvesting orders data
  const today = new Date();
  const startDate = formatDate(
    new Date(today.getFullYear(), today.getMonth() - 1, 1),
    'yyyy-MM-dd'
  );
  const endDate = formatDate(
    new Date(today.getFullYear(), today.getMonth() + 2, 0),
    'yyyy-MM-dd'
  );
  const [labourVarieties, setLabourVarieties] = useState<
    HarvestingLabourVariety[]
  >([]);
  const [query] = useLazySchedulesForOrderQuery();

  // Find the matching harvesting order based on the workOrder's orderId
  useEffect(() => {
    async function fetchHarvestingOrders() {
      if (workOrder) {
        setCrewSize(workOrder.crewSize.toString());

        const { data } = await query({
          orderId: workOrder.orderId,
        });
        const matchingOrder = data?.order;
        if (matchingOrder) {
          setHarvestingOrder(matchingOrder);
          setLabourVarieties(
            matchingOrder.varieties.map((v) => ({
              varietyName: v.name,
              harvested: 0,
              thrownOut: 0,
            }))
          );
        }
      }
    }

    fetchHarvestingOrders();
  }, [query, workOrder]);

  const handleTransitionAfterEnter = () => {
    setComments('');
  };

  const handleCrewSizeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCrewSize(e.target.value);
  };

  const handlePauseChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setComments(e.target.value);
  };

  const handleSaveClick = () => {
    const crewSizeValue = isNaN(parseInt(crewSize, 10))
        ? 1
        : parseInt(crewSize, 10),
      varieties = labourVarieties
        .map((v) => ({ ...v }))
        .filter((v) => v.harvested > 0 || v.thrownOut > 0);

    onRecord({
      comments,
      crewSize: crewSizeValue,
      varieties,
      finalHarvest,
    });
    onClose();
  };

  const handleVarietyChange = (
    varietyName: string,
    property: 'harvested' | 'thrownOut',
    value: number
  ) => {
    const varieties = labourVarieties.map((v) => ({ ...v })),
      variety = varieties.find((v) => v.varietyName === varietyName);

    if (variety) {
      variety[property] = value;
    }
    setLabourVarieties(varieties);
  };

  return (
    <HeadlessUI.Transition.Root
      show={open}
      as={Fragment}
      afterEnter={handleTransitionAfterEnter}
    >
      <HeadlessUI.Dialog as="div" className="relative z-10" onClose={onClose}>
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-0 text-center">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-0 scale-95"
              enterTo="opacity-100 translate-y-0 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 scale-100"
              leaveTo="opacity-0 translate-y-0 scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative my-8 w-full max-w-2xl transform overflow-hidden rounded-lg bg-white p-6 text-left shadow-xl transition-all">
                <div>
                  <div className="mt-3 text-center">
                    <HeadlessUI.Dialog.Title
                      as="h3"
                      className="text-base font-semibold leading-6 text-gray-900"
                    >
                      Record Harvest
                    </HeadlessUI.Dialog.Title>
                    <form className="mt-5">
                      <div className="mt-5">
                        <div className="text-left">
                          <label>Crew Size</label>
                          <input
                            type="number"
                            name="comments"
                            className="block w-40 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                            value={crewSize}
                            onChange={handleCrewSizeChange}
                          />
                        </div>
                        <table className="min-w-full divide-y divide-gray-300 text-sm">
                          <thead>
                            <tr>
                              <th className="w-1 p-2">Variety</th>
                              <th className="w-1 p-2 text-right">Planted</th>
                              <th className="w-1 p-2 text-right">
                                Previous Harvest
                              </th>
                              <th className="w-1 p-2 text-right">Remaining </th>
                              <th className="whitespace-nowrap p-2 text-center">
                                Harvested
                              </th>
                              <th className="whitespace-nowrap p-2 text-center">
                                Throw Out
                              </th>
                              <th>&nbsp;</th>
                            </tr>
                          </thead>
                          <tbody>
                            {workOrder.varieties.map((variety) => (
                              <RecordLabourDialogItem
                                key={variety.name}
                                harvestingOrder={harvestingOrder}
                                variety={variety}
                                history={history}
                                onChange={handleVarietyChange}
                              />
                            ))}
                          </tbody>
                        </table>
                      </div>

                      <div className="text-left">
                        <label>Comments</label>
                        <textarea
                          name="comments"
                          className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                          value={comments}
                          onChange={handlePauseChange}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-500">
                          Harvest Complete?
                        </label>
                        <div>
                          <HeadlessUI.Switch
                            checked={finalHarvest}
                            onChange={setFinalHarvest}
                            className={classNames(
                              finalHarvest ? 'bg-blue-400' : 'bg-gray-200',
                              'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                            )}
                          >
                            <span
                              aria-hidden="true"
                              className={classNames(
                                finalHarvest
                                  ? 'translate-x-5'
                                  : 'translate-x-0',
                                'pointer-events-none inline-block size-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                              )}
                            />
                          </HeadlessUI.Switch>
                        </div>
                      </div>
                    </form>
                  </div>
                </div>
                <div className="mt-6 text-right">
                  <button
                    type="button"
                    className="btn-secondary"
                    onClick={onClose}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn-primary ml-2"
                    onClick={handleSaveClick}
                  >
                    Record Harvest
                  </button>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}
