import React, { Fragment, useRef, useState, useEffect } from 'react';
import * as HeadlessUI from '@headlessui/react';
import * as settings from 'api/models/settings';
import { useAppDispatch } from '@/services/hooks';
import { Icon } from '@/components/icon';
import {
  createUpgradeOption,
  updateUpgradeOption,
} from './upgrade-options-slice';

interface UpgradeOptionDetailProps {
  option: settings.UpgradeOption | null;
  close: () => void;
}

export function UpgradeOptionDetail({
  option,
  close,
}: UpgradeOptionDetailProps) {
  const dispatch = useAppDispatch(),
    firstRef = useRef<HTMLInputElement>(null),
    [containerPickDescription, setContainerPickDescription] = useState<
      string | null
    >(null),
    [origins, setOrigins] = useState<string | null>(null),
    [costs, setCosts] = useState<string | null>(null);

  useEffect(() => {
    if (option) {
      setContainerPickDescription(option.containerPickDescription);
      setOrigins(option.origins);
      setCosts(option.costs);
    }
  }, [option]);

  const handleContainerPickDescriptionChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setContainerPickDescription(e.target.value);
  };

  const handleOriginsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setOrigins(e.target.value);
  };

  const handleCostsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCosts(e.target.value);
  };

  const handleCancelClick = () => {
    close();
  };

  const handleSaveClick = async () => {
    if (option) {
      if (option.id) {
        const arg = { id: option.id, containerPickDescription, origins, costs },
          response = await dispatch(updateUpgradeOption(arg));
        if (!('error' in response)) {
          close();
        }
      } else {
        const arg = { id: 0, containerPickDescription, origins, costs },
          response = await dispatch(createUpgradeOption(arg));
        if (!('error' in response)) {
          close();
        }
      }
    }
  };

  const handleClose = () => {
    close();
  };

  return (
    <HeadlessUI.Transition.Root show={true} as={Fragment}>
      <HeadlessUI.Dialog
        as="div"
        className="relative z-30"
        initialFocus={firstRef}
        onClose={handleClose}
      >
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex items-center justify-center p-0 text-center">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-0 scale-95"
              enterTo="opacity-100 translate-y-0 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 scale-100"
              leaveTo="opacity-0 translate-y-0 scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative h-screen w-screen transform overflow-hidden p-6 transition-all">
                <div className="mx-auto flex max-w-xl flex-col rounded-lg bg-white p-6 text-left shadow-xl">
                  <div className="mb-4 flex justify-center border-b-2 pb-4">
                    <HeadlessUI.Dialog.Title
                      as="h3"
                      className="text-lg font-medium leading-6 text-gray-900"
                    >
                      <Icon
                        icon="calendar-day"
                        className="h-6 w-6"
                        aria-hidden="true"
                      />
                      &nbsp; Edit Upgrade Option
                    </HeadlessUI.Dialog.Title>
                  </div>
                  <div className="flex flex-grow flex-col">
                    <form className="flex w-full">
                      <div className="mx-auto grid max-w-2xl grid-cols-2 items-start gap-4">
                        <div className="col-span-2">
                          <label
                            htmlFor="container-pick-description"
                            className="block"
                          >
                            Container / Pick Description
                          </label>
                          <input
                            type="text"
                            id="container-pick-description"
                            autoComplete="off"
                            value={containerPickDescription || ''}
                            ref={firstRef}
                            onChange={handleContainerPickDescriptionChange}
                            className="block w-full min-w-0 flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          />
                        </div>
                        <div>
                          <label htmlFor="origins" className="block">
                            Origins
                          </label>
                          <input
                            type="text"
                            id="origins"
                            autoComplete="off"
                            value={origins || ''}
                            onChange={handleOriginsChange}
                            className="block w-full min-w-0 flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          />
                        </div>
                        <div>
                          <label htmlFor="costs" className="block">
                            Costs
                          </label>
                          <input
                            type="text"
                            id="costs"
                            autoComplete="off"
                            value={costs || ''}
                            onChange={handleCostsChange}
                            className="block w-full min-w-0 flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          />
                        </div>
                      </div>
                    </form>
                  </div>

                  <div className="mt-4 flex justify-end border-t-2 pt-4">
                    <button
                      type="button"
                      className="btn-secondary text-lg"
                      onClick={handleCancelClick}
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      className="btn-primary ml-4 text-lg"
                      onClick={handleSaveClick}
                    >
                      Save&nbsp;
                      <Icon icon="save" className="ml-2" />
                    </button>
                  </div>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}
